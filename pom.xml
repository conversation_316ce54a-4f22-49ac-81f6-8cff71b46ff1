<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.shuyun.apaas</groupId>
        <artifactId>apaas-devkit-parent</artifactId>
        <version>1.7.1</version>
    </parent>

    <groupId>com.shuyun.fast</groupId>
    <artifactId>fast-connector-jdb</artifactId>
    <version>1.0.1-SNAPSHOT</version>

    <name>${project.artifactId}</name>

    <properties>
        <uuid.version>4.3.0</uuid.version>
        <fast.sdk.version>0.1.20</fast.sdk.version>
        <dataapi.version>1.40.0.RELEASE</dataapi.version>
        <redisson.version>3.23.3</redisson.version>
        <kafka.client.version>3.6.2</kafka.client.version>
        <es.eventservice.version>1.9.6.RC3</es.eventservice.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.shuyun.apaas</groupId>
            <artifactId>apaas-connector-devkit-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.apaas</groupId>
            <artifactId>apaas-connector-devkit-kylin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lcap.qing</groupId>
            <artifactId>qing-retrofit</artifactId>
            <version>1.0.10</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>fast-open-sdk</artifactId>
            <version>${fast.sdk.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>dm-dataapi-sdk</artifactId>
                    <groupId>com.shuyun.dm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dm-sdk-common</artifactId>
                    <groupId>com.shuyun.dm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.ticket</groupId>
            <artifactId>ticket-support</artifactId>
            <version>1.12.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.kylin.crm</groupId>
                    <artifactId>data-selector-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-dataapi-sdk</artifactId>
            <version>${dataapi.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.lite.module</groupId>
                    <artifactId>lite-passport-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>discovery-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>discovery-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>client-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>client-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>configuration-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.spectrum</groupId>
                    <artifactId>configuration-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>joda-time</artifactId>
                    <groupId>joda-time</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>ant</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.1.8</version>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka.client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.es</groupId>
            <artifactId>es-eventservice-sdk</artifactId>
            <version>${es.eventservice.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>lite-sdk-core</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lite-passport-sdk</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>discovery-client</artifactId>
                    <groupId>com.shuyun.spectrum</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lite-base</artifactId>
                    <groupId>com.shuyun.lite.module</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.bval</groupId>
            <artifactId>bval-jsr</artifactId>
            <version>2.0.6</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.air.kylin</groupId>
            <artifactId>micronaut-support</artifactId>
            <version>1.0.0</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.hibernate.validator</groupId>-->
<!--            <artifactId>hibernate-validator</artifactId>-->
<!--            <version>6.2.0.Final</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.glassfish</groupId>-->
<!--            <artifactId>jakarta.el</artifactId>-->
<!--            <version>3.0.3</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <version>${uuid.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.shuyun.apaas</groupId>
                <artifactId>apaas-connector-maven-plugin</artifactId>
                <configuration>
                    <!-- 是否跳过此插件 -->
                    <skip>false</skip>
                    <username>mengjiaxing2141</username>
                    <password>CGskAfhW</password>
                    <!-- 用户名，可配置到环境变量或自行修改 -->
                    <!--元数据推送地址，请看文档-->
<!--                    <url>https://graph.dev-apaas.shuyun.com</url>-->
                    <url>https://graph.apaas.shuyun.com</url>
                    <!-- 连接器主类 -->
                    <mainClass>com.shuyun.apaas.connector.fast.FastConnector</mainClass>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>apaas</id>
            <url>https://nexus.shuyun.com/repository/public</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>apaas</name>
            <url>https://nexus.shuyun.com/repository/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>apaas</name>
            <url>https://nexus.shuyun.com/repository/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
