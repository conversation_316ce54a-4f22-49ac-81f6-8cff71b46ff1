# 日期格式异常解决方案

## 问题描述

在调用EpassportClient.userInfo时遇到Jackson日期反序列化异常：

```
com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.util.Date` from String "2025-07-21 16:55:59": not a valid representation
```

## 问题原因

1. **日期格式不匹配**: Epassport服务返回的日期格式是`"yyyy-MM-dd HH:mm:ss"`
2. **Jackson期望格式**: <PERSON>默认期望ISO 8601格式（如`"2025-07-21T16:55:59.000Z"`）
3. **反序列化失败**: 格式不匹配导致User对象的created字段无法正确反序列化

## 解决方案

### 方案1：修改EpassportClient配置（推荐）

如果EpassportClient是通过Retrofit创建的，可以配置自定义的Gson/Jackson转换器：

```java
@Configuration
public class EpassportClientConfig {
    
    @Bean
    public EpassportClient epassportClient() {
        Gson gson = new GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();
            
        return Retrofit.Builder()
            .baseUrl("https://your-epassport-url")
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
            .create(EpassportClient.class);
    }
}
```

### 方案2：使用Jackson自定义配置

```java
@Bean
@Primary
public ObjectMapper epassportObjectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    
    // 设置日期格式
    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    mapper.setDateFormat(dateFormat);
    
    // 配置反序列化特性
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    
    return mapper;
}
```

### 方案3：自定义User类（如果可以修改）

如果可以修改User类，可以添加自定义的日期格式注解：

```java
public class User {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date created;
    
    // 其他字段...
}
```

### 方案4：异常处理和降级（当前实现）

在EpassportService中检测日期格式异常并提供友好的错误信息：

```java
private boolean isDateFormatException(UndeclaredThrowableException e) {
    Throwable cause = e.getCause();
    if (cause == null) {
        return false;
    }
    
    String className = cause.getClass().getSimpleName();
    String message = cause.getMessage();
    
    return className.contains("InvalidFormatException") && 
           message != null && 
           (message.contains("Date") || message.contains("date"));
}
```

## 临时解决方案

### 1. 修改Epassport服务端

如果可以修改Epassport服务，建议将日期格式改为ISO 8601标准格式：

```json
{
  "created": "2025-07-21T16:55:59.000Z"
}
```

### 2. 使用字符串字段

如果无法修改日期格式，可以考虑将User类中的Date字段改为String：

```java
public class User {
    private String created; // 改为String类型
    
    // 提供转换方法
    public Date getCreatedAsDate() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.parse(created);
        } catch (ParseException e) {
            return null;
        }
    }
}
```

### 3. 使用代理模式

创建一个User的代理类来处理日期转换：

```java
public class UserProxy {
    private Map<String, Object> rawData;
    
    public Date getCreated() {
        String dateStr = (String) rawData.get("created");
        if (dateStr != null) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            } catch (ParseException e) {
                return null;
            }
        }
        return null;
    }
}
```

## 测试验证

### 1. 单元测试

```java
@Test
public void testDateFormatException() {
    // 模拟日期格式异常
    UndeclaredThrowableException exception = createDateFormatException();
    
    boolean isDateFormat = epassportService.isDateFormatException(exception);
    assertTrue(isDateFormat);
}

private UndeclaredThrowableException createDateFormatException() {
    InvalidFormatException cause = new InvalidFormatException(
        null, "Cannot deserialize Date", "2025-07-21 16:55:59", Date.class);
    return new UndeclaredThrowableException(cause);
}
```

### 2. 集成测试

```java
@Test
public void testUserInfoWithDateFormat() {
    // 使用真实的token测试
    String token = "Bearer your-test-token";
    
    try {
        User user = epassportService.getUserInfo(token);
        assertNotNull(user);
    } catch (ApiException e) {
        // 验证错误信息是否友好
        assertTrue(e.getMessage().contains("日期格式"));
    }
}
```

## 监控和告警

### 1. 添加指标

```java
@Component
public class DateFormatMetrics {
    private final Counter dateFormatErrorCounter = 
        Counter.builder("epassport.date.format.error").register(Metrics.globalRegistry);
    
    public void recordDateFormatError() {
        dateFormatErrorCounter.increment();
    }
}
```

### 2. 日志监控

```yaml
# logback配置
<logger name="com.shuyun.apaas.connector.fast.service.v1_0_0.EpassportService" level="WARN">
    <appender-ref ref="DATE_FORMAT_ERROR"/>
</logger>
```

## 最佳实践

### 1. 统一日期格式

在整个系统中使用统一的日期格式，建议使用ISO 8601标准。

### 2. 版本兼容性

在API设计时考虑向后兼容性，支持多种日期格式。

### 3. 错误处理

提供清晰的错误信息，帮助开发者快速定位问题。

### 4. 文档说明

在API文档中明确说明日期字段的格式要求。

## 长期解决方案

1. **协调服务端**: 与Epassport服务团队协调，统一日期格式
2. **升级依赖**: 升级Jackson版本，使用更灵活的日期处理
3. **标准化**: 制定团队内部的日期格式标准
4. **工具类**: 开发通用的日期转换工具类

通过以上方案，可以有效解决日期格式异常问题，提高系统的稳定性和用户体验。
