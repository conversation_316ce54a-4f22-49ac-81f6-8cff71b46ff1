# 单点登录接口说明

## 概述

本接口参考门店同步接口的实现模式，提供单点登录功能。通过用户提供的访问令牌（token），调用EpassportClient.userInfo方法获取用户信息，并返回标准化的用户数据。

## 接口信息

- **接口名称**: 单点登录
- **API名称**: `auth.sso.login`
- **请求方式**: POST
- **接口类型**: 查询接口

## 核心类说明

### 1. SsoLoginParam.java
单点登录参数类，继承自ApiBaseParam：

#### 必填字段
- `token` - 访问令牌（最大512字符，必填）

#### 系统字段
- `tenantId` - 租户ID（继承自ApiBaseParam）
- `bizCode` - 业务编码（继承自ApiBaseParam）
- `requestChannel` - 请求渠道（继承自ApiBaseParam）
- `requestSystem` - 请求系统（继承自ApiBaseParam）
- `transactionId` - 事务ID（继承自ApiBaseParam）

### 2. SsoLoginResult.java
单点登录响应类，包含完整的用户信息：

#### 用户基本信息
- `id` - 用户ID
- `type` - 用户类型
- `realname` - 真实姓名
- `username` - 用户名
- `employeeNo` - 员工编号
- `enabled` - 是否启用
- `locked` - 是否锁定
- `mobile` - 手机号

#### 角色信息
- `roles` - 角色列表（List<RoleInfo>格式）

### 3. RoleInfo.java
角色信息类：

- `id` - 角色ID
- `name` - 角色名称
- `roleType` - 角色类型

### 4. SsoLoginApiHandler.java
单点登录处理器，继承自AbstractApiHandler：

- **validate()**: 参数验证，包括token格式验证
- **beforeRequest()**: 请求前处理，可处理Bearer前缀
- **prepareParam()**: 参数准备
- **request()**: 调用服务层执行登录
- **prepareResult()**: 结果处理

### 5. AuthService.java
认证服务类：

- **ssoLogin()**: 执行单点登录逻辑
- **buildSsoLoginResult()**: 构建登录结果
- 使用反射机制适配不同版本的UserResponseInfo

## 使用示例

### 1. 基本登录示例

```java
// 创建登录参数
SsoLoginParam param = new SsoLoginParam();

// 设置基础信息
param.setTenantId("your-tenant-id");
param.setBizCode("your-biz-code");
param.setRequestChannel("WEB");
param.setRequestSystem("YOUR_SYSTEM");
param.setTransactionId("TXN" + System.currentTimeMillis());

// 设置访问令牌（必填）
param.setToken("your-access-token");

// 执行登录
SsoLoginResult result = authService.ssoLogin(param);

// 处理结果
if (result != null) {
    System.out.println("登录成功，用户ID：" + result.getId());
    System.out.println("用户名：" + result.getUsername());
    System.out.println("真实姓名：" + result.getRealname());
    System.out.println("角色数量：" + result.getRoles().size());
}
```

### 2. HTTP请求示例

```json
POST /api/auth/sso/login
Content-Type: application/json

{
  "tenantId": "your-tenant-id",
  "bizCode": "your-biz-code",
  "requestChannel": "WEB",
  "requestSystem": "YOUR_SYSTEM",
  "transactionId": "TXN1704067200000",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 3. 响应示例

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    "id": "USER001",
    "type": "ADMIN",
    "realname": "张三",
    "username": "zhangsan",
    "employeeNo": "EMP001",
    "enabled": true,
    "locked": false,
    "mobile": "13800138000",
    "roles": [
      {
        "id": "ROLE001",
        "name": "系统管理员",
        "roleType": "ADMIN"
      },
      {
        "id": "ROLE002",
        "name": "业务用户",
        "roleType": "USER"
      }
    ]
  }
}
```

## 技术实现

### 1. EpassportClient集成
接口通过EpassportClient.userInfo方法获取用户信息：

```java
UserResponseInfo userResponseInfo = epassportClient.userInfo(param.getToken());
```

### 2. 反射机制适配
由于UserResponseInfo的结构可能因版本而异，使用反射机制获取字段值：

```java
// 尝试多个可能的字段名
private String getFieldValue(Object obj, String... fieldNames) {
    for (String fieldName : fieldNames) {
        try {
            // 尝试getter方法
            String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method method = obj.getClass().getMethod(getterName);
            Object value = method.invoke(obj);
            if (value != null) {
                return String.valueOf(value);
            }
        } catch (Exception e) {
            // 尝试直接字段访问
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value != null) {
                return String.valueOf(value);
            }
        }
    }
    return null;
}
```

### 3. 字段映射策略
为了适配不同的字段命名，使用多个可能的字段名：

- **用户ID**: id, userId, ID
- **用户名**: username, userName, loginName, account
- **真实姓名**: realname, realName, fullName, name
- **员工编号**: employeeNo, employeeNumber, empNo, workNo
- **手机号**: mobile, phone, phoneNumber, cellphone
- **启用状态**: enabled, isEnabled, active, isActive
- **锁定状态**: locked, isLocked, disabled, isDisabled

## 数据验证

### 1. 参数验证
- token字段不能为空
- token长度必须大于等于10个字符
- 继承ApiBaseParam的基础验证

### 2. Token格式处理
- 支持Bearer前缀的token
- 自动处理不同格式的token

### 3. 业务验证
- 验证token的有效性
- 检查用户状态（启用/锁定）

## 错误处理

### 1. 常见错误码
- `500100`: 系统异常
- `400`: 参数验证失败
- 自定义错误：无效的访问令牌

### 2. 异常处理策略
- 参数验证异常：返回具体的验证错误信息
- Token无效异常：返回"无效的访问令牌"
- 系统异常：返回通用的系统错误信息

## 安全考虑

### 1. Token安全
- Token应该通过HTTPS传输
- Token应该有合理的过期时间
- 避免在日志中记录完整的token

### 2. 用户信息保护
- 敏感信息应该根据权限返回
- 记录访问日志用于审计

### 3. 防护措施
- 实现访问频率限制
- 监控异常登录行为
- 及时清理过期token

## 性能优化

### 1. 缓存策略
- 可以考虑缓存用户信息（短时间）
- 缓存角色信息减少重复查询

### 2. 异步处理
- 用户信息获取可以异步处理
- 日志记录异步化

### 3. 连接池优化
- 优化EpassportClient的连接池配置
- 设置合理的超时时间

## 监控指标

### 1. 业务指标
- 登录成功率
- 平均响应时间
- 用户活跃度

### 2. 技术指标
- API调用次数
- 错误率统计
- 系统资源使用情况

## 扩展功能

### 1. 多租户支持
- 基于tenantId的用户隔离
- 不同租户的权限控制

### 2. 审计日志
- 记录登录行为
- 追踪用户操作

### 3. 权限控制
- 基于角色的权限验证
- 细粒度的功能权限

## 注意事项

1. **Token管理**: 确保token的安全传输和存储
2. **用户状态**: 检查用户的启用和锁定状态
3. **角色权限**: 正确处理用户的角色信息
4. **异常处理**: 实现完善的异常处理机制
5. **日志记录**: 记录关键操作便于问题排查
6. **性能监控**: 监控接口性能和用户体验
