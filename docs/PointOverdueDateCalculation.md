# 积分过期日期计算说明

## 概述

在积分服务中，当用户没有指定生效时间和过期时间时，系统需要自动计算过期日期。对于POINT类型的积分，过期日期设置为"次年同月最后1天"。

## 计算规则

### 基本规则
- **POINT类型**: 过期日期 = 次年同月最后1天的23:59:59.999999999
- **其他类型**: 过期日期 = 当前时间 + 1年

### 具体算法

```java
private LocalDateTime calculateOverdueDate(LocalDateTime currentTime, String pointBizType) {
    if ("POINT".equals(pointBizType)) {
        // 次年同月最后1天
        LocalDateTime nextYear = currentTime.plusYears(1);
        
        // 获取次年同月的最后一天
        LocalDateTime lastDayOfMonth = nextYear.withDayOfMonth(1)  // 先设置为当月第一天
                .plusMonths(1)  // 加一个月
                .minusDays(1)   // 减一天，得到上个月的最后一天
                .withHour(23)   // 设置为当天的最后时刻
                .withMinute(59)
                .withSecond(59)
                .withNano(999999999);
        
        return lastDayOfMonth;
    } else {
        // 其他类型默认加一年
        return currentTime.plusYears(1);
    }
}
```

## 计算示例

### 示例1：普通月份
- **当前时间**: 2024年3月15日 10:30:00
- **过期时间**: 2025年3月31日 23:59:59.999999999
- **说明**: 3月有31天，所以过期日期是3月31日

### 示例2：2月份（平年）
- **当前时间**: 2024年2月15日 14:20:00
- **过期时间**: 2025年2月28日 23:59:59.999999999
- **说明**: 2025年是平年，2月只有28天

### 示例3：2月份（闰年）
- **当前时间**: 2023年2月15日 09:45:00
- **过期时间**: 2024年2月29日 23:59:59.999999999
- **说明**: 2024年是闰年，2月有29天

### 示例4：30天月份
- **当前时间**: 2024年4月15日 12:00:00
- **过期时间**: 2025年4月30日 23:59:59.999999999
- **说明**: 4月有30天，所以过期日期是4月30日

### 示例5：12月份
- **当前时间**: 2024年12月10日 16:00:00
- **过期时间**: 2025年12月31日 23:59:59.999999999
- **说明**: 12月有31天，所以过期日期是12月31日

### 示例6：非POINT类型
- **当前时间**: 2024年3月15日 10:30:45
- **过期时间**: 2025年3月15日 10:30:45
- **说明**: 非POINT类型直接加一年，保持原有的时分秒

## 边界情况处理

### 1. 闰年处理
- 自动识别闰年和平年
- 2月份会根据年份自动调整天数（28天或29天）

### 2. 月末日期
- 如果当前是月末（如1月31日），次年同月最后一天仍然是月末
- 不同月份的天数差异会自动处理

### 3. 跨年处理
- 12月份的过期日期正确计算为次年12月31日
- 年份正确递增

## 时间精度

过期时间设置为当天的最后时刻：
- **小时**: 23
- **分钟**: 59
- **秒**: 59
- **纳秒**: 999999999

这确保积分在过期日期的整天内都有效。

## 代码实现

### 主要方法
```java
// 在PointService.java中
private LocalDateTime calculateOverdueDate(LocalDateTime currentTime, String pointBizType)
```

### 调用位置
```java
// 当生效时间或过期时间为空时调用
if(ObjectUtils.isEmpty(param.getEffectTime())||ObjectUtils.isEmpty(param.getExpiredTime())){
    LocalDateTime localDateTime = LocalDateTime.now();
    LocalDateTime overdue = calculateOverdueDate(localDateTime, param.getPointBizType());
    
    request.setEffectiveDate(DateUtil.utcTime(localDateTime));
    request.setOverdueDate(DateUtil.utcTime(overdue));
}
```

## 测试用例

详细的测试用例请参考 `PointServiceOverdueDateTest.java`，包含：

1. **普通月份测试** - 验证31天月份的计算
2. **2月份测试** - 验证平年和闰年的处理
3. **30天月份测试** - 验证30天月份的计算
4. **边界情况测试** - 验证月末和跨年的处理
5. **非POINT类型测试** - 验证其他类型的处理

## 注意事项

1. **时区处理**: 计算使用LocalDateTime，最终通过DateUtil.utcTime()转换为UTC时间
2. **类型判断**: 只有pointBizType为"POINT"时才使用特殊计算规则
3. **空值处理**: 只有在生效时间或过期时间为空时才会触发自动计算
4. **精度保证**: 使用纳秒级精度确保时间的准确性

## 业务意义

这个计算规则确保了：
1. **用户友好**: 积分在整个过期月份都有效
2. **规则统一**: 所有POINT类型积分使用相同的过期规则
3. **时间充足**: 给用户足够的时间使用积分
4. **边界清晰**: 过期时间明确到具体的日期和时刻

通过这种方式，积分系统可以提供一致且用户友好的过期时间计算。
