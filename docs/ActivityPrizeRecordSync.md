# 活动领奖记录同步接口说明

## 概述

本接口参考门店同步接口（kylin.crm.mdm.shop.sync）的实现模式，提供活动领奖记录的同步功能。接口支持邀请类活动的奖励发放记录同步，采用标准的数据同步架构。

## 接口信息

- **接口名称**: 活动领奖记录同步
- **API名称**: `activity.prizeRecord.sync`
- **请求方式**: POST
- **接口类型**: 同步接口
- **数据模型FQN**: `data.prctvmkt.activity.%s.ActivityPrizeRecord`

## 核心类说明

### 1. ActivityPrizeRecordSyncParam.java
活动领奖记录同步参数类，继承自ApiBaseParam：

#### 必填字段
- `actId` - 活动ID（最大64字符）
- `actName` - 活动名称（最大200字符）
- `productName` - 活动产品（最大200字符）
- `userId` - 用户ID（被邀请，最大64字符）
- `nickname` - 昵称（被邀请，最大100字符）
- `phone` - 手机号（被邀请，最大20字符）
- `inviteTime` - 邀请时间
- `inviterId` - 邀请人ID（最大64字符）
- `inviterName` - 邀请人名字（最大100字符）
- `inviterPhone` - 邀请人手机号（最大20字符）
- `inviterChannelId` - 邀请人所属渠道ID（最大64字符）
- `inviterChannelName` - 邀请人所属渠道名称（最大200字符）

#### 可选字段
- `id` - 记录ID（系统自动生成或手动指定）
- `gpsLocation` - 经纬度（最大100字符）
- `provinceName` - 省（最大50字符）
- `cityName` - 市（最大50字符）
- `areaName` - 区（最大50字符）
- `extension` - 扩展字段（Map类型）

#### 系统字段
- `memberType` - 会员类型（系统自动设置）
- `channelType` - 渠道类型（系统自动设置）
- `isValid` - 是否有效（默认"Y"）
- `lastSync` - 最后同步时间（系统自动设置）

### 2. ActivityPrizeRecordSyncApiHandler.java
活动领奖记录同步处理器，继承自AbstractApiHandler：

- **validate()**: 参数验证
- **beforeRequest()**: 请求前处理，设置记录ID和渠道类型
- **prepareParam()**: 参数准备，时区转换等
- **request()**: 调用服务层执行同步
- **prepareResult()**: 结果处理

### 3. ActivityService.java
活动服务类，参考MdmService实现：

- **bizCacheGet()**: 获取活动缓存配置
- **prizeRecordSync()**: 执行活动领奖记录同步

### 4. ActivityCache.java
活动缓存配置类，继承自SyncCache：

- `memberType` - 会员类型
- `activityConfig` - 活动配置信息
- `enabled` - 是否启用

## 使用示例

### 1. 基本同步示例

```java
// 创建同步参数
ActivityPrizeRecordSyncParam param = new ActivityPrizeRecordSyncParam();

// 设置基础信息
param.setTenantId("your-tenant-id");
param.setBizCode("your-biz-code");
param.setRequestChannel("APP");
param.setRequestSystem("YOUR_SYSTEM");
param.setTransactionId("TXN" + System.currentTimeMillis());

// 设置活动信息（必填）
param.setActId("ACT20240101001");
param.setActName("春节邀请好友活动");
param.setProductName("现金红包");

// 设置被邀请用户信息（必填）
param.setUserId("USER001");
param.setNickname("张三");
param.setPhone("***********");
param.setInviteTime(LocalDateTime.now());

// 设置邀请人信息（必填）
param.setInviterId("INVITER001");
param.setInviterName("李四");
param.setInviterPhone("***********");
param.setInviterChannelId("CHANNEL001");
param.setInviterChannelName("微信小程序");

// 设置地理位置信息（可选）
param.setGpsLocation("116.397128,39.916527");
param.setProvinceName("北京市");
param.setCityName("北京市");
param.setAreaName("朝阳区");

// 设置扩展字段（可选）
Map<String, Object> extension = new HashMap<>();
extension.put("businessNo", "BIZ20240101001");
extension.put("remark", "春节活动邀请奖励");
extension.put("rewardAmount", 10.00);
param.setExtension(extension);

// 执行同步
Void result = activityService.prizeRecordSync(param);
```

### 2. HTTP请求示例

```json
POST /api/activity/prizeRecord/sync
Content-Type: application/json

{
  "tenantId": "your-tenant-id",
  "bizCode": "your-biz-code",
  "requestChannel": "APP",
  "requestSystem": "YOUR_SYSTEM",
  "transactionId": "TXN1704067200000",
  "actId": "ACT20240101001",
  "actName": "春节邀请好友活动",
  "productName": "现金红包",
  "userId": "USER001",
  "nickname": "张三",
  "phone": "***********",
  "inviteTime": "2024-01-01T10:00:00",
  "inviterId": "INVITER001",
  "inviterName": "李四",
  "inviterPhone": "***********",
  "inviterChannelId": "CHANNEL001",
  "inviterChannelName": "微信小程序",
  "gpsLocation": "116.397128,39.916527",
  "provinceName": "北京市",
  "cityName": "北京市",
  "areaName": "朝阳区",
  "extension": {
    "businessNo": "BIZ20240101001",
    "remark": "春节活动邀请奖励",
    "rewardAmount": 10.00
  }
}
```

### 3. 响应示例

```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

## 数据同步机制

### 1. 记录ID生成规则
如果未提供记录ID，系统会自动生成：
```
格式：{actId}_{userId}_{inviterId}_{timestamp}
示例：ACT001_USER001_INVITER001_1704067200
```

### 2. 数据过滤
系统会自动过滤以下字段，不会同步到数据存储：
- `tenantId`
- `bizCode`
- `requestChannel`
- `requestSystem`
- `transactionId`
- `extension`（扩展字段会单独处理）

### 3. 扩展字段处理
`extension` 字段中的数据会被展开并合并到主数据中，支持动态字段扩展。

## 配置要求

### 1. 缓存配置
需要在BizCache中配置活动相关的缓存信息：
- 缓存类型：`activity`
- 会员类型：根据业务需要配置

### 2. 数据模型
系统会自动创建数据模型：
- FQN：`data.prctvmkt.activity.{memberType}.ActivityPrizeRecord`
- 支持动态字段扩展

## 错误处理

### 1. 常见错误码
- `500001`: 缓存配置不存在
- `400`: 参数验证失败
- `500`: 系统内部错误

### 2. 参数验证
- 所有必填字段不能为空
- 字符串长度不能超过限制
- 邀请时间格式必须正确

## 性能优化

### 1. 批量处理
建议使用批量处理方式提高性能：
- 单次处理建议不超过1000条记录
- 可以通过扩展字段传递批次信息

### 2. 幂等性
- 通过记录ID保证幂等性
- 相同ID的记录会被更新而不是重复创建

## 监控指标

### 1. 业务指标
- 同步成功率
- 平均处理耗时
- 记录数量统计

### 2. 技术指标
- API调用次数
- 错误率统计
- 系统资源使用情况

## 注意事项

1. **时区处理**: 邀请时间建议使用LocalDateTime，系统会自动处理时区转换
2. **数据一致性**: 确保活动ID、用户ID等关键字段的一致性
3. **扩展字段**: 合理使用扩展字段，避免存储过大的数据
4. **缓存配置**: 确保正确配置活动缓存信息
5. **错误处理**: 实现适当的错误处理和重试机制
6. **日志记录**: 关键操作需要记录详细日志便于排查问题

## 扩展功能

### 1. 数据查询
可以基于数据模型实现查询功能：
- 按活动ID查询
- 按用户ID查询
- 按时间范围查询

### 2. 统计分析
支持基于同步数据进行统计分析：
- 活动参与度统计
- 邀请效果分析
- 地域分布分析

### 3. 数据导出
支持将同步数据导出为各种格式：
- Excel格式
- CSV格式
- JSON格式
