# 单点登录接口故障排查指南

## 常见问题及解决方案

### 1. UndeclaredThrowableException 异常

#### 问题描述
```
java.lang.reflect.UndeclaredThrowableException: null
```

#### 可能原因
1. **EpassportClient代理问题**: 如果EpassportClient是通过代理（如Retrofit、Feign等）创建的，可能存在代理配置问题
2. **网络连接问题**: 无法连接到Epassport服务
3. **Token格式问题**: Token格式不正确或缺少Bearer前缀
4. **反射调用异常**: 在获取User对象字段时发生异常

#### 解决方案

##### 1. 检查EpassportClient配置

```java
@Configuration
public class EpassportClientConfig {
    
    @Bean
    public EpassportClient epassportClient() {
        return Retrofit.Builder()
            .baseUrl("https://your-epassport-url")
            .addConverterFactory(GsonConverterFactory.create())
            .client(createOkHttpClient())
            .build()
            .create(EpassportClient.class);
    }
    
    private OkHttpClient createOkHttpClient() {
        return new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .addInterceptor(new LoggingInterceptor())
            .build();
    }
}
```

##### 2. 增强异常处理

```java
public SsoLoginResult ssoLogin(SsoLoginParam param) {
    try {
        log.info("开始处理单点登录，token长度：{}", param.getToken().length());

        // 验证token格式
        if (param.getToken() == null || param.getToken().trim().isEmpty()) {
            throw new ApiException(ApiTags.API_RESP_CODE_400, "访问令牌不能为空");
        }

        // 准备Authorization header
        String authHeader = param.getToken().startsWith("Bearer ") ? 
            param.getToken() : "Bearer " + param.getToken();

        // 调用EpassportClient获取用户信息
        User userInfo;
        try {
            userInfo = epassportClient.userInfo(authHeader);
        } catch (Exception e) {
            log.error("调用EpassportClient失败", e);
            if (e instanceof UndeclaredThrowableException) {
                Throwable cause = e.getCause();
                if (cause != null) {
                    log.error("UndeclaredThrowableException原因：", cause);
                    throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                        "认证服务异常：" + cause.getMessage());
                }
            }
            throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                "认证服务调用失败：" + e.getMessage());
        }

        if (userInfo == null) {
            log.warn("获取用户信息失败，token无效");
            throw new ApiException(ApiTags.API_RESP_CODE_401, "无效的访问令牌");
        }

        // 构建登录结果
        SsoLoginResult result = buildSsoLoginResult(userInfo);

        log.info("单点登录成功，用户ID：{}，用户名：{}", result.getId(), result.getUsername());
        return result;

    } catch (ApiException e) {
        log.error("单点登录业务异常：{}", e.getMessage());
        throw e;
    } catch (Exception e) {
        log.error("单点登录系统异常", e);
        throw new ApiException(ApiTags.API_RESP_CODE_500100, "系统异常，请稍后重试");
    }
}
```

##### 3. 添加网络连接检查

```java
@Component
public class EpassportHealthChecker {
    
    private final EpassportClient epassportClient;
    
    public EpassportHealthChecker(EpassportClient epassportClient) {
        this.epassportClient = epassportClient;
    }
    
    public boolean isEpassportAvailable() {
        try {
            // 可以调用一个健康检查接口
            // 或者使用一个测试token进行验证
            return true;
        } catch (Exception e) {
            log.warn("Epassport服务不可用", e);
            return false;
        }
    }
}
```

##### 4. 改进反射异常处理

```java
private String getFieldValue(Object obj, String... fieldNames) {
    if (obj == null || fieldNames == null) {
        return null;
    }

    for (String fieldName : fieldNames) {
        try {
            // 尝试通过getter方法获取
            String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            Method method = obj.getClass().getMethod(getterName);
            Object value = method.invoke(obj);
            if (value != null) {
                return String.valueOf(value);
            }
        } catch (NoSuchMethodException e) {
            // 方法不存在，尝试下一个
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.warn("调用方法{}失败: {}", fieldName, e.getMessage());
        } catch (Exception e) {
            log.warn("获取字段{}时发生异常: {}", fieldName, e.getMessage());
        }

        try {
            // 尝试直接访问字段
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value != null) {
                return String.valueOf(value);
            }
        } catch (NoSuchFieldException e) {
            // 字段不存在，尝试下一个
        } catch (IllegalAccessException e) {
            log.warn("访问字段{}失败: {}", fieldName, e.getMessage());
        } catch (Exception e) {
            log.warn("获取字段{}时发生异常: {}", fieldName, e.getMessage());
        }
    }

    return null;
}
```

### 2. Token相关问题

#### 问题：Token格式错误
```
解决方案：确保token包含正确的Bearer前缀
```

#### 问题：Token过期
```
解决方案：实现token刷新机制
```

#### 问题：Token权限不足
```
解决方案：检查token的权限范围
```

### 3. 网络连接问题

#### 问题：连接超时
```yaml
# 配置连接超时
epassport:
  client:
    connect-timeout: 30s
    read-timeout: 30s
    write-timeout: 30s
```

#### 问题：SSL证书问题
```java
// 配置SSL信任
OkHttpClient.Builder builder = new OkHttpClient.Builder();
if (trustAllCerts) {
    builder.sslSocketFactory(createTrustAllSSLSocketFactory(), trustAllCertsManager);
    builder.hostnameVerifier((hostname, session) -> true);
}
```

### 4. 调试技巧

#### 1. 启用详细日志
```xml
<logger name="com.shuyun.apaas.connector.fast.service.v1_0_0.AuthService" level="DEBUG"/>
<logger name="com.shuyun.apaas.connector.fast.client.EpassportClient" level="DEBUG"/>
```

#### 2. 添加请求拦截器
```java
public class LoggingInterceptor implements Interceptor {
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        log.info("请求URL: {}", request.url());
        log.info("请求头: {}", request.headers());
        
        Response response = chain.proceed(request);
        log.info("响应码: {}", response.code());
        
        return response;
    }
}
```

#### 3. 使用断点调试
在以下位置设置断点：
- `epassportClient.userInfo(authHeader)` 调用前后
- `buildSsoLoginResult(userInfo)` 方法内部
- 反射方法调用处

### 5. 预防措施

#### 1. 健康检查
```java
@Component
public class SsoHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 检查Epassport服务状态
            boolean isHealthy = checkEpassportHealth();
            return isHealthy ? Health.up().build() : Health.down().build();
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
```

#### 2. 熔断器
```java
@Component
public class EpassportCircuitBreaker {
    
    @CircuitBreaker(name = "epassport", fallbackMethod = "fallbackUserInfo")
    public User getUserInfo(String token) {
        return epassportClient.userInfo(token);
    }
    
    public User fallbackUserInfo(String token, Exception ex) {
        log.warn("Epassport服务不可用，使用降级方案", ex);
        throw new ApiException(ApiTags.API_RESP_CODE_503, "认证服务暂时不可用");
    }
}
```

#### 3. 缓存机制
```java
@Cacheable(value = "userInfo", key = "#token")
public User getUserInfoWithCache(String token) {
    return epassportClient.userInfo(token);
}
```

### 6. 监控告警

#### 1. 错误率监控
```java
@Component
public class SsoMetrics {
    
    private final Counter successCounter = Counter.builder("sso.login.success").register(Metrics.globalRegistry);
    private final Counter errorCounter = Counter.builder("sso.login.error").register(Metrics.globalRegistry);
    
    public void recordSuccess() {
        successCounter.increment();
    }
    
    public void recordError(String errorType) {
        errorCounter.increment(Tags.of("error.type", errorType));
    }
}
```

#### 2. 响应时间监控
```java
@Timed(name = "sso.login.duration", description = "SSO login duration")
public SsoLoginResult ssoLogin(SsoLoginParam param) {
    // 登录逻辑
}
```

通过以上措施，可以有效解决UndeclaredThrowableException异常，并提高系统的稳定性和可观测性。
