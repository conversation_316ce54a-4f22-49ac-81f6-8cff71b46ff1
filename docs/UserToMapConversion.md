# User类型改为Map类型解决方案

## 概述

为了解决Jackson日期格式反序列化异常，我们将EpassportClient.userInfo的返回类型从强类型`User`改为灵活的`Map<String, Object>`类型。这样可以避免日期格式不匹配导致的反序列化失败。

## 主要改动

### 1. EpassportService.java

#### 方法签名变更
```java
// 修改前
public User getUserInfo(String authHeader) throws ApiException

// 修改后  
public Map<String, Object> getUserInfo(String authHeader) throws ApiException
```

#### 新增转换方法
```java
/**
 * 将User对象转换为Map
 */
private Map<String, Object> convertToMap(Object userObj) {
    if (userObj == null) {
        return null;
    }
    
    try {
        Map<String, Object> userMap = new HashMap<>();
        Class<?> clazz = userObj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            field.setAccessible(true);
            Object value = field.get(userObj);
            
            // 处理日期字段，转换为字符串
            if (value instanceof Date) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                value = sdf.format((Date) value);
            }
            
            userMap.put(field.getName(), value);
        }
        
        return userMap;
    } catch (Exception e) {
        log.error("转换User对象为Map失败", e);
        throw new RuntimeException("用户信息转换失败", e);
    }
}
```

### 2. AuthService.java

#### 方法参数类型变更
```java
// 修改前
private SsoLoginResult buildSsoLoginResult(User userInfo)
private String getUserId(User userInfo)
private String getUserType(User userInfo)
// ... 其他方法

// 修改后
private SsoLoginResult buildSsoLoginResult(Map<String, Object> userInfo)
private String getUserId(Map<String, Object> userInfo)  
private String getUserType(Map<String, Object> userInfo)
// ... 其他方法
```

#### 新增Map处理工具方法
```java
/**
 * 从Map中获取字符串值
 */
private String getMapValue(Map<String, Object> map, String... fieldNames) {
    if (map == null || fieldNames == null) {
        return null;
    }

    for (String fieldName : fieldNames) {
        Object value = map.get(fieldName);
        if (value != null) {
            return String.valueOf(value);
        }
    }

    return null;
}

/**
 * 从Map中获取布尔值
 */
private Boolean getBooleanMapValue(Map<String, Object> map, String... fieldNames) {
    String value = getMapValue(map, fieldNames);
    if (value == null) {
        return null;
    }
    
    try {
        return Boolean.parseBoolean(value);
    } catch (Exception e) {
        return null;
    }
}

/**
 * 从Map中获取对象值
 */
private Object getMapValueAsObject(Map<String, Object> map, String... fieldNames) {
    if (map == null || fieldNames == null) {
        return null;
    }

    for (String fieldName : fieldNames) {
        Object value = map.get(fieldName);
        if (value != null) {
            return value;
        }
    }

    return null;
}
```

## 优势

### 1. 解决日期格式问题
- **避免反序列化异常**: Map类型不会因为日期格式不匹配而失败
- **灵活处理**: 可以在转换时自定义日期格式处理
- **向后兼容**: 支持多种日期格式

### 2. 提高灵活性
- **字段容错**: 即使服务端添加新字段也不会影响解析
- **类型安全**: 通过工具方法提供类型转换
- **易于扩展**: 可以轻松添加新的字段处理逻辑

### 3. 降低耦合
- **减少依赖**: 不再依赖具体的User类结构
- **版本兼容**: 适配不同版本的Epassport服务
- **接口稳定**: 即使User类结构变化也不影响功能

## 使用示例

### 1. 获取用户信息（API调用不变）
```java
SsoLoginParam param = new SsoLoginParam();
param.setToken("your-access-token");

SsoLoginResult result = authService.ssoLogin(param);
// 返回的结果结构完全不变
```

### 2. 内部处理变化
```java
// 原来的方式（强类型）
User user = epassportClient.userInfo(token);
String userId = user.getId();

// 现在的方式（Map类型）
Map<String, Object> userInfo = epassportService.getUserInfo(token);
String userId = getMapValue(userInfo, "id", "userId", "ID");
```

### 3. 日期处理
```java
// 自动处理日期格式转换
Object userObj = epassportClient.userInfo(authHeader);
Map<String, Object> userMap = convertToMap(userObj);

// 日期字段已经转换为字符串格式
String createdTime = (String) userMap.get("created"); // "2025-07-21 16:55:59"
```

## 测试验证

### 1. 单元测试
```java
@Test
public void testMapConversion() {
    // 模拟User对象
    Object mockUser = createMockUser();
    
    // 转换为Map
    Map<String, Object> userMap = epassportService.convertToMap(mockUser);
    
    // 验证转换结果
    assertNotNull(userMap);
    assertTrue(userMap.containsKey("id"));
    assertTrue(userMap.containsKey("username"));
}

@Test
public void testDateHandling() {
    // 测试日期字段处理
    Map<String, Object> userInfo = new HashMap<>();
    userInfo.put("created", "2025-07-21 16:55:59");
    
    String userId = getMapValue(userInfo, "id");
    String created = getMapValue(userInfo, "created");
    
    assertNotNull(created);
    assertEquals("2025-07-21 16:55:59", created);
}
```

### 2. 集成测试
```java
@Test
public void testSsoLoginWithMapType() {
    SsoLoginParam param = new SsoLoginParam();
    param.setToken("test-token");
    
    SsoLoginResult result = authService.ssoLogin(param);
    
    // 验证结果结构不变
    assertNotNull(result);
    assertNotNull(result.getId());
    assertNotNull(result.getUsername());
}
```

## 注意事项

### 1. 类型安全
- 使用工具方法进行类型转换
- 添加空值检查和异常处理
- 提供默认值处理

### 2. 性能考虑
- 反射操作有一定性能开销
- 可以考虑缓存字段信息
- 对于高频调用可以优化转换逻辑

### 3. 维护性
- 保持字段名映射的一致性
- 及时更新字段名列表
- 添加详细的日志记录

## 回滚方案

如果需要回滚到User类型，只需要：

1. 恢复方法签名
2. 移除Map转换逻辑
3. 恢复原来的反射方法
4. 解决日期格式问题（通过配置）

## 总结

通过将User类型改为Map类型，我们成功解决了日期格式异常问题，同时提高了系统的灵活性和容错性。这个改动对外部API接口没有任何影响，用户可以继续使用原有的调用方式。
