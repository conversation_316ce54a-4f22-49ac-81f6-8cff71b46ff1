# 日期计算逻辑验证

## 实现的算法

```java
private LocalDateTime calculateOverdueDate(LocalDateTime currentTime, String pointBizType) {
    if ("POINT".equals(pointBizType)) {
        // 次年同月最后1天
        LocalDateTime nextYear = currentTime.plusYears(1);
        
        // 获取次年同月的最后一天
        LocalDateTime lastDayOfMonth = nextYear.withDayOfMonth(1)  // 先设置为当月第一天
                .plusMonths(1)  // 加一个月
                .minusDays(1)   // 减一天，得到上个月的最后一天
                .withHour(23)   // 设置为当天的最后时刻
                .withMinute(59)
                .withSecond(59)
                .with<PERSON><PERSON>(999999999);
        
        return lastDayOfMonth;
    } else {
        // 其他类型默认加一年
        return currentTime.plusYears(1);
    }
}
```

## 验证结果

### 测试案例1：2024年3月15日
- **输入**: 2024-03-15 10:30:00
- **计算过程**:
  1. 加一年: 2025-03-15
  2. 设为月初: 2025-03-01
  3. 加一个月: 2025-04-01
  4. 减一天: 2025-03-31
  5. 设为最后时刻: 2025-03-31 23:59:59.999999999
- **预期结果**: 2025-03-31 23:59:59.999999999 ✅

### 测试案例2：2024年2月15日（闰年到平年）
- **输入**: 2024-02-15 14:20:00
- **计算过程**:
  1. 加一年: 2025-02-15
  2. 设为月初: 2025-02-01
  3. 加一个月: 2025-03-01
  4. 减一天: 2025-02-28
  5. 设为最后时刻: 2025-02-28 23:59:59.999999999
- **预期结果**: 2025-02-28 23:59:59.999999999 ✅

### 测试案例3：2023年2月15日（平年到闰年）
- **输入**: 2023-02-15 09:45:00
- **计算过程**:
  1. 加一年: 2024-02-15
  2. 设为月初: 2024-02-01
  3. 加一个月: 2024-03-01
  4. 减一天: 2024-02-29
  5. 设为最后时刻: 2024-02-29 23:59:59.999999999
- **预期结果**: 2024-02-29 23:59:59.999999999 ✅

### 测试案例4：2024年4月15日（30天月份）
- **输入**: 2024-04-15 12:00:00
- **计算过程**:
  1. 加一年: 2025-04-15
  2. 设为月初: 2025-04-01
  3. 加一个月: 2025-05-01
  4. 减一天: 2025-04-30
  5. 设为最后时刻: 2025-04-30 23:59:59.999999999
- **预期结果**: 2025-04-30 23:59:59.999999999 ✅

### 测试案例5：2024年12月10日（年末月份）
- **输入**: 2024-12-10 16:00:00
- **计算过程**:
  1. 加一年: 2025-12-10
  2. 设为月初: 2025-12-01
  3. 加一个月: 2026-01-01
  4. 减一天: 2025-12-31
  5. 设为最后时刻: 2025-12-31 23:59:59.999999999
- **预期结果**: 2025-12-31 23:59:59.999999999 ✅

### 测试案例6：2024年1月31日（月末日期）
- **输入**: 2024-01-31 23:59:59
- **计算过程**:
  1. 加一年: 2025-01-31
  2. 设为月初: 2025-01-01
  3. 加一个月: 2025-02-01
  4. 减一天: 2025-01-31
  5. 设为最后时刻: 2025-01-31 23:59:59.999999999
- **预期结果**: 2025-01-31 23:59:59.999999999 ✅

### 测试案例7：非POINT类型
- **输入**: 2024-03-15 10:30:45 (pointBizType = "OTHER")
- **计算过程**: 直接加一年
- **预期结果**: 2025-03-15 10:30:45 ✅

## 算法正确性验证

### 优点
1. **自动处理闰年**: 通过Java的LocalDateTime API自动处理闰年逻辑
2. **月份天数自适应**: 自动适配不同月份的天数（28/29/30/31天）
3. **精确到纳秒**: 设置为当天的最后时刻，确保整天有效
4. **边界情况处理**: 正确处理年末、月末等边界情况

### 算法步骤解析
1. `plusYears(1)`: 获取次年同月同日
2. `withDayOfMonth(1)`: 设置为当月第一天
3. `plusMonths(1)`: 移动到下个月第一天
4. `minusDays(1)`: 回退到上个月最后一天
5. `withHour(23).withMinute(59).withSecond(59).withNano(999999999)`: 设置为当天最后时刻

### 为什么不直接使用YearMonth.lengthOfMonth()？
当前算法更加直观和可靠：
- 避免了手动计算月份天数
- 利用Java内置的日期计算逻辑
- 自动处理所有边界情况

## 在PointService中的应用

```java
if(ObjectUtils.isEmpty(param.getEffectTime())||ObjectUtils.isEmpty(param.getExpiredTime())){
    LocalDateTime localDateTime = LocalDateTime.now();
    LocalDateTime overdue = calculateOverdueDate(localDateTime, param.getPointBizType());
    
    request.setEffectiveDate(DateUtil.utcTime(localDateTime));
    request.setOverdueDate(DateUtil.utcTime(overdue));
}
```

### 关键点
1. **条件触发**: 只有当生效时间或过期时间为空时才自动计算
2. **类型判断**: 只有POINT类型使用特殊规则
3. **UTC转换**: 最终通过DateUtil.utcTime()转换为UTC时间
4. **当前时间**: 使用LocalDateTime.now()作为基准时间

## 总结

实现的算法完全符合"Overdue = 次年同月最后1天"的需求：
- ✅ 正确处理所有月份的天数差异
- ✅ 正确处理闰年和平年
- ✅ 正确处理边界情况
- ✅ 设置为当天的最后时刻
- ✅ 支持非POINT类型的默认处理

算法简洁、可靠，充分利用了Java 8的时间API的优势。
