# 单点登录接口集成指南

## 快速开始

### 1. 基本集成

```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @Inject
    private ApiHandlerRouter handlerRouter;
    
    @PostMapping("/sso/login")
    public ApiResult<SsoLoginResult> ssoLogin(@RequestBody SsoLoginParam param) {
        return handlerRouter.route(param).handle(param);
    }
}
```

### 2. 前端调用示例

```javascript
// JavaScript/TypeScript 示例
async function ssoLogin(token) {
    const response = await fetch('/api/auth/sso/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tenantId: 'your-tenant-id',
            bizCode: 'your-biz-code',
            requestChannel: 'WEB',
            requestSystem: 'YOUR_SYSTEM',
            transactionId: 'TXN' + Date.now(),
            token: token
        })
    });
    
    const result = await response.json();
    
    if (result.success) {
        console.log('登录成功:', result.data);
        // 保存用户信息到本地存储
        localStorage.setItem('userInfo', JSON.stringify(result.data));
        return result.data;
    } else {
        console.error('登录失败:', result.message);
        throw new Error(result.message);
    }
}
```

### 3. 移动端集成示例

```java
// Android 示例
public class SsoLoginRequest {
    private String tenantId;
    private String bizCode;
    private String requestChannel = "MOBILE";
    private String requestSystem = "ANDROID_APP";
    private String transactionId;
    private String token;
    
    // getters and setters...
}

public class AuthApiClient {
    private static final String BASE_URL = "https://your-api-domain.com";
    
    public SsoLoginResult ssoLogin(String token) throws Exception {
        SsoLoginRequest request = new SsoLoginRequest();
        request.setTenantId("your-tenant-id");
        request.setBizCode("your-biz-code");
        request.setTransactionId("TXN" + System.currentTimeMillis());
        request.setToken(token);
        
        // 使用 Retrofit 或其他 HTTP 客户端
        Call<ApiResult<SsoLoginResult>> call = apiService.ssoLogin(request);
        Response<ApiResult<SsoLoginResult>> response = call.execute();
        
        if (response.isSuccessful() && response.body().isSuccess()) {
            return response.body().getData();
        } else {
            throw new Exception("登录失败: " + response.body().getMessage());
        }
    }
}
```

## 配置说明

### 1. EpassportClient配置

```yaml
# application.yml
epassport:
  base-url: https://epassport.your-domain.com
  timeout: 30s
  retry-count: 3
```

```java
@Configuration
public class EpassportConfig {
    
    @Bean
    public EpassportClient epassportClient(@Value("${epassport.base-url}") String baseUrl) {
        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(EpassportClient.class);
    }
}
```

### 2. 安全配置

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/sso/login").permitAll()
                .anyRequest().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt.decoder(jwtDecoder()))
            );
        return http.build();
    }
}
```

## 错误处理

### 1. 统一异常处理

```java
@ControllerAdvice
public class AuthExceptionHandler {
    
    @ExceptionHandler(ApiException.class)
    public ResponseEntity<ApiResult<Void>> handleApiException(ApiException e) {
        ApiResult<Void> result = new ApiResult<>();
        result.setSuccess(false);
        result.setCode(e.getCode());
        result.setMessage(e.getMessage());
        return ResponseEntity.ok(result);
    }
    
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResult<Void>> handleValidationException(IllegalArgumentException e) {
        ApiResult<Void> result = new ApiResult<>();
        result.setSuccess(false);
        result.setCode("400");
        result.setMessage("参数验证失败: " + e.getMessage());
        return ResponseEntity.badRequest().body(result);
    }
}
```

### 2. 前端错误处理

```javascript
function handleSsoLoginError(error) {
    switch (error.code) {
        case '500100':
            showMessage('系统异常，请稍后重试');
            break;
        case '400':
            showMessage('参数错误: ' + error.message);
            break;
        default:
            if (error.message.includes('无效的访问令牌')) {
                // 重新获取token或跳转到登录页
                redirectToLogin();
            } else {
                showMessage('登录失败: ' + error.message);
            }
    }
}
```

## 最佳实践

### 1. Token管理

```javascript
class TokenManager {
    static setToken(token) {
        // 加密存储token
        const encryptedToken = encrypt(token);
        localStorage.setItem('access_token', encryptedToken);
    }
    
    static getToken() {
        const encryptedToken = localStorage.getItem('access_token');
        return encryptedToken ? decrypt(encryptedToken) : null;
    }
    
    static clearToken() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('userInfo');
    }
    
    static isTokenValid() {
        const token = this.getToken();
        if (!token) return false;
        
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            return payload.exp * 1000 > Date.now();
        } catch (e) {
            return false;
        }
    }
}
```

### 2. 用户状态管理

```javascript
class UserManager {
    static async getCurrentUser() {
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
            return JSON.parse(userInfo);
        }
        
        // 如果本地没有用户信息，尝试通过token获取
        const token = TokenManager.getToken();
        if (token && TokenManager.isTokenValid()) {
            try {
                const user = await ssoLogin(token);
                return user;
            } catch (e) {
                TokenManager.clearToken();
                return null;
            }
        }
        
        return null;
    }
    
    static hasRole(roleType) {
        const user = this.getCurrentUser();
        return user && user.roles && 
               user.roles.some(role => role.roleType === roleType);
    }
    
    static hasPermission(permission) {
        const user = this.getCurrentUser();
        // 根据用户角色判断权限
        return user && user.enabled && !user.locked;
    }
}
```

### 3. 自动刷新机制

```javascript
class AuthInterceptor {
    static setupAxiosInterceptors() {
        // 请求拦截器
        axios.interceptors.request.use(
            config => {
                const token = TokenManager.getToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            error => Promise.reject(error)
        );
        
        // 响应拦截器
        axios.interceptors.response.use(
            response => response,
            async error => {
                if (error.response?.status === 401) {
                    // Token过期，尝试刷新
                    try {
                        const newToken = await refreshToken();
                        TokenManager.setToken(newToken);
                        // 重试原请求
                        return axios.request(error.config);
                    } catch (refreshError) {
                        // 刷新失败，跳转到登录页
                        TokenManager.clearToken();
                        window.location.href = '/login';
                    }
                }
                return Promise.reject(error);
            }
        );
    }
}
```

## 测试指南

### 1. 单元测试

```java
@ExtendWith(MockitoExtension.class)
class AuthServiceTest {
    
    @Mock
    private EpassportClient epassportClient;
    
    @InjectMocks
    private AuthService authService;
    
    @Test
    void testSsoLoginSuccess() {
        // 准备测试数据
        SsoLoginParam param = new SsoLoginParam();
        param.setToken("valid-token");
        
        UserResponseInfo mockUser = createMockUser();
        when(epassportClient.userInfo("valid-token")).thenReturn(mockUser);
        
        // 执行测试
        SsoLoginResult result = authService.ssoLogin(param);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("USER001", result.getId());
        assertEquals("testuser", result.getUsername());
    }
    
    @Test
    void testSsoLoginWithInvalidToken() {
        SsoLoginParam param = new SsoLoginParam();
        param.setToken("invalid-token");
        
        when(epassportClient.userInfo("invalid-token")).thenReturn(null);
        
        assertThrows(ApiException.class, () -> authService.ssoLogin(param));
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SsoLoginIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testSsoLoginEndpoint() {
        SsoLoginParam param = new SsoLoginParam();
        param.setTenantId("test-tenant");
        param.setBizCode("test-biz");
        param.setToken("test-token");
        
        ResponseEntity<ApiResult> response = restTemplate.postForEntity(
            "/api/auth/sso/login", param, ApiResult.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
    }
}
```

## 部署注意事项

### 1. 环境配置

```bash
# 生产环境变量
export EPASSPORT_BASE_URL=https://epassport.prod.com
export JWT_SECRET=your-jwt-secret
export REDIS_URL=redis://redis.prod.com:6379
```

### 2. 监控配置

```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

### 3. 日志配置

```xml
<configuration>
    <appender name="AUTH_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/auth.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/auth.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.shuyun.apaas.connector.fast.service.v1_0_0.AuthService" level="INFO" additivity="false">
        <appender-ref ref="AUTH_LOG"/>
    </logger>
</configuration>
```
