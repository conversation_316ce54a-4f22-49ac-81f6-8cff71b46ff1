# 活动缓存配置说明

## 概述

活动领奖记录同步接口需要在BizCache中配置相应的缓存信息，用于指定数据存储的目标模型和相关配置。

## 配置结构

### BizCache配置示例

```json
{
  "id": "activity_cache_001",
  "tenantId": "your-tenant-id",
  "bizCode": "your-biz-code",
  "cacheType": "activity",
  "memberType": "standard",
  "activityConfig": "{\"enabled\":true,\"maxRecords\":10000}",
  "enabled": true,
  "createTime": "2024-01-01T00:00:00",
  "updateTime": "2024-01-01T00:00:00"
}
```

### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | String | 是 | 缓存配置的唯一标识 |
| tenantId | String | 是 | 租户ID |
| bizCode | String | 是 | 业务编码 |
| cacheType | String | 是 | 缓存类型，固定值"activity" |
| memberType | String | 是 | 会员类型，用于构建数据模型FQN |
| activityConfig | String | 否 | 活动配置信息（JSON字符串） |
| enabled | Boolean | 是 | 是否启用，默认true |

## 会员类型说明

会员类型用于构建数据模型的FQN，不同的会员类型对应不同的数据存储模型：

### 常用会员类型
- `standard` - 标准会员
- `vip` - VIP会员
- `premium` - 高级会员
- `enterprise` - 企业会员

### 数据模型FQN
根据会员类型，系统会自动构建数据模型FQN：
```
data.prctvmkt.activity.{memberType}.ActivityPrizeRecord
```

示例：
- 标准会员：`data.prctvmkt.activity.standard.ActivityPrizeRecord`
- VIP会员：`data.prctvmkt.activity.vip.ActivityPrizeRecord`

## 活动配置说明

`activityConfig` 字段存储活动相关的配置信息，格式为JSON字符串：

### 配置示例
```json
{
  "enabled": true,
  "maxRecords": 10000,
  "retentionDays": 365,
  "autoCleanup": true,
  "notificationEnabled": true,
  "customFields": {
    "rewardType": "string",
    "rewardAmount": "number",
    "expiryDate": "datetime"
  }
}
```

### 配置字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| enabled | Boolean | 是否启用活动记录同步 |
| maxRecords | Integer | 最大记录数限制 |
| retentionDays | Integer | 数据保留天数 |
| autoCleanup | Boolean | 是否自动清理过期数据 |
| notificationEnabled | Boolean | 是否启用通知功能 |
| customFields | Object | 自定义字段定义 |

## 配置管理

### 1. 创建配置

```java
// 创建活动缓存配置
ActivityCache cache = new ActivityCache();
cache.setId("activity_cache_001");
cache.setTenantId("your-tenant-id");
cache.setBizCode("your-biz-code");
cache.setCacheType("activity");
cache.setMemberType("standard");
cache.setActivityConfig("{\"enabled\":true,\"maxRecords\":10000}");
cache.setEnabled(true);

// 保存配置
bizCacheService.save(cache);
```

### 2. 查询配置

```java
// 查询活动缓存配置
List<ActivityCache> caches = bizCacheService.get(
    ActivityCache.class, 
    tenantId, 
    bizCode, 
    BizCache.ACTIVITY
);
```

### 3. 更新配置

```java
// 更新配置
ActivityCache cache = bizCacheService.getById(ActivityCache.class, cacheId);
cache.setActivityConfig("{\"enabled\":true,\"maxRecords\":20000}");
bizCacheService.update(cache);
```

## 多租户配置

### 租户隔离
每个租户需要独立的缓存配置：

```json
[
  {
    "tenantId": "tenant-a",
    "bizCode": "biz-001",
    "cacheType": "activity",
    "memberType": "standard"
  },
  {
    "tenantId": "tenant-b", 
    "bizCode": "biz-002",
    "cacheType": "activity",
    "memberType": "vip"
  }
]
```

### 业务隔离
同一租户下不同业务可以有不同的配置：

```json
[
  {
    "tenantId": "tenant-a",
    "bizCode": "marketing",
    "cacheType": "activity",
    "memberType": "standard"
  },
  {
    "tenantId": "tenant-a",
    "bizCode": "promotion", 
    "cacheType": "activity",
    "memberType": "premium"
  }
]
```

## 配置验证

### 1. 必填字段验证
- tenantId不能为空
- bizCode不能为空
- cacheType必须为"activity"
- memberType不能为空

### 2. 格式验证
- activityConfig必须是有效的JSON字符串
- enabled必须是布尔值

### 3. 业务验证
- 同一租户+业务编码下只能有一个活动缓存配置
- memberType必须是系统支持的类型

## 故障排查

### 1. 常见问题

#### 缓存配置不存在
```
错误信息：ApiException: 500001, activity
解决方案：检查BizCache中是否存在对应的活动缓存配置
```

#### 会员类型不匹配
```
错误信息：数据模型不存在
解决方案：确认memberType配置正确，且对应的数据模型已创建
```

#### 配置格式错误
```
错误信息：JSON解析失败
解决方案：检查activityConfig字段的JSON格式是否正确
```

### 2. 调试方法

#### 查看配置
```java
// 打印当前配置
ActivityCache cache = activityService.bizCacheGet(param);
log.info("Activity cache config: {}", JsonUtil.serialize(cache));
```

#### 验证数据模型
```java
// 验证数据模型FQN
String fqn = String.format(ModelTags.DATA_FQN_ACTIVITY_PRIZE_RECORD, cache.getMemberType());
log.info("Data model FQN: {}", fqn);
```

## 最佳实践

### 1. 配置管理
- 使用版本控制管理配置变更
- 定期备份配置数据
- 建立配置变更审批流程

### 2. 性能优化
- 合理设置maxRecords限制
- 启用自动清理功能
- 监控数据增长趋势

### 3. 安全考虑
- 限制配置修改权限
- 记录配置变更日志
- 定期审计配置合规性

### 4. 监控告警
- 监控配置可用性
- 设置数据量告警阈值
- 监控同步成功率
