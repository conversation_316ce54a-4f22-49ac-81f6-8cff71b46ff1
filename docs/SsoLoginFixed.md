# 单点登录接口修复说明

## 问题解决

### 原始问题
```
java.lang.reflect.UndeclaredThrowableException: null
```

### 解决方案
创建了一个新的`EpassportService`包装器来安全地处理`EpassportClient`调用中的异常。

## 修复内容

### 1. 新增EpassportService类
```java
@Singleton
@Slf4j
public class EpassportService {
    
    private final EpassportClient epassportClient;

    @Inject
    public EpassportService(ClientManager clientManager) {
        this.epassportClient = clientManager.getEpassportClient();
    }

    public User getUserInfo(String authHeader) throws ApiException {
        try {
            return epassportClient.userInfo(authHeader);
        } catch (UndeclaredThrowableException e) {
            return handleUndeclaredThrowableException(e);
        } catch (Exception e) {
            throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                "认证服务调用失败：" + e.getMessage());
        }
    }
}
```

### 2. 更新AuthService
```java
@Singleton
@Slf4j
public class AuthService {
    
    private final EpassportService epassportService;

    @Inject
    public AuthService(EpassportService epassportService) {
        this.epassportService = epassportService;
    }

    public SsoLoginResult ssoLogin(SsoLoginParam param) {
        try {
            // 验证并格式化token
            String authHeader = epassportService.validateAndFormatToken(param.getToken());
            
            // 调用EpassportService获取用户信息
            User userInfo = epassportService.getUserInfo(authHeader);
            
            // 构建登录结果
            return buildSsoLoginResult(userInfo);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "系统异常，请稍后重试");
        }
    }
}
```

## 主要改进

### 1. 异常处理增强
- **UndeclaredThrowableException处理**: 专门处理代理调用中的异常
- **根本原因分析**: 解析异常的根本原因并提供有意义的错误信息
- **HTTP状态码映射**: 将HTTP错误码映射为业务错误码

### 2. Token验证和格式化
```java
public String validateAndFormatToken(String token) {
    if (token == null || token.trim().isEmpty()) {
        throw new ApiException(ApiTags.API_RESP_CODE_400, "访问令牌不能为空");
    }
    
    String trimmedToken = token.trim();
    
    if (trimmedToken.length() < 10) {
        throw new ApiException(ApiTags.API_RESP_CODE_400, "访问令牌格式不正确");
    }
    
    if (!trimmedToken.startsWith("Bearer ") && !trimmedToken.startsWith("bearer ")) {
        return "Bearer " + trimmedToken;
    }
    
    return trimmedToken;
}
```

### 3. 详细的异常分类处理
```java
private User handleUndeclaredThrowableException(UndeclaredThrowableException e) throws ApiException {
    Throwable cause = e.getCause();
    
    // 网络异常
    if (cause instanceof ConnectException) {
        throw new ApiException(ApiTags.API_RESP_CODE_503, "无法连接到认证服务");
    }
    
    if (cause instanceof SocketTimeoutException) {
        throw new ApiException(ApiTags.API_RESP_CODE_504, "认证服务响应超时");
    }
    
    // HTTP异常
    if (cause.getClass().getSimpleName().contains("HttpException")) {
        String message = cause.getMessage();
        if (message != null && message.contains("401")) {
            throw new ApiException(ApiTags.API_RESP_CODE_401, "访问令牌无效或已过期");
        }
        // ... 其他HTTP状态码处理
    }
    
    // JSON解析异常
    if (cause.getClass().getSimpleName().contains("JsonSyntaxException")) {
        throw new ApiException(ApiTags.API_RESP_CODE_500100, "认证服务响应格式异常");
    }
    
    // 兜底处理
    throw new ApiException(ApiTags.API_RESP_CODE_500100, 
        "认证服务调用失败：" + cause.getMessage());
}
```

## 使用方法

### 1. 基本调用（不变）
```java
SsoLoginParam param = new SsoLoginParam();
param.setTenantId("your-tenant-id");
param.setBizCode("your-biz-code");
param.setToken("your-access-token");

SsoLoginResult result = authService.ssoLogin(param);
```

### 2. 错误处理
```java
try {
    SsoLoginResult result = authService.ssoLogin(param);
    // 处理成功结果
} catch (ApiException e) {
    switch (e.getCode()) {
        case "400":
            // 参数错误
            break;
        case "401":
            // 认证失败
            break;
        case "503":
            // 服务不可用
            break;
        case "504":
            // 服务超时
            break;
        default:
            // 其他错误
            break;
    }
}
```

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 400 | 参数错误 | 检查token格式和必填参数 |
| 401 | 认证失败 | Token无效或已过期，需要重新获取 |
| 403 | 权限不足 | Token权限不够，检查权限范围 |
| 404 | 接口不存在 | 检查Epassport服务配置 |
| 502 | 上游服务错误 | Epassport服务内部错误 |
| 503 | 服务不可用 | 无法连接到Epassport服务 |
| 504 | 服务超时 | Epassport服务响应超时 |
| 500100 | 系统异常 | 其他未分类的系统错误 |

## 测试验证

### 1. 运行单元测试
```bash
./mvnw test -Dtest=EpassportServiceTest
./mvnw test -Dtest=AuthServiceTest
```

### 2. 集成测试
```bash
curl -X POST http://localhost:8080/api/auth/sso/login \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "test-tenant",
    "bizCode": "test-biz",
    "token": "your-test-token"
  }'
```

## 监控建议

### 1. 添加指标监控
```java
@Component
public class SsoMetrics {
    private final Counter successCounter = Counter.builder("sso.login.success").register(Metrics.globalRegistry);
    private final Counter errorCounter = Counter.builder("sso.login.error").register(Metrics.globalRegistry);
    
    public void recordSuccess() {
        successCounter.increment();
    }
    
    public void recordError(String errorType) {
        errorCounter.increment(Tags.of("error.type", errorType));
    }
}
```

### 2. 健康检查
```java
@Component
public class SsoHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        try {
            boolean isHealthy = epassportService.isHealthy();
            return isHealthy ? Health.up().build() : Health.down().build();
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}
```

## 注意事项

1. **Token安全**: 确保token通过HTTPS传输
2. **日志记录**: 不要在日志中记录完整的token
3. **缓存策略**: 可以考虑短时间缓存用户信息
4. **重试机制**: 对于网络异常可以实现重试
5. **熔断器**: 在高并发场景下考虑使用熔断器

通过以上修复，`UndeclaredThrowableException`异常已经得到妥善处理，系统的稳定性和可观测性都得到了显著提升。
