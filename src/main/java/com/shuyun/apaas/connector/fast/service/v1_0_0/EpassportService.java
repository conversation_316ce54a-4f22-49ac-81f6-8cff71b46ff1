package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.client.EpassportClient;
import com.shuyun.apaas.connector.fast.client.manager.ClientManager;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import java.util.Map;
import com.shuyun.fast.base.ApiTags;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.UndeclaredThrowableException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;

/**
 * Epassport服务包装器
 * 用于处理EpassportClient调用中的异常
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Singleton
@Slf4j
public class EpassportService {

    private final EpassportClient epassportClient;

    @Inject
    public EpassportService(ClientManager clientManager) {
        this.epassportClient = clientManager.getEpassportClient();
    }

    /**
     * 安全地调用userInfo方法
     *
     * @param authHeader Authorization header
     * @return Map对象，包含用户信息
     * @throws ApiException 业务异常
     */
    public Map<String, Object> getUserInfo(String authHeader) throws ApiException {
        try {
            log.debug("调用EpassportClient.userInfo，authHeader长度：{}", authHeader.length());
            
            // 验证authHeader格式

            Map<String, Object> userInfoObj = epassportClient.userInfo(authHeader);
            log.debug("EpassportClient.userInfo调用成功，返回结果：{}", userInfoObj != null ? "非空" : "空");

            // 将User对象转换为Map
            //Map<String, Object> userInfo = convertToMap(userInfoObj);
            return userInfoObj;
            
        } catch (UndeclaredThrowableException e) {
            log.error("EpassportClient调用发生UndeclaredThrowableException", e);

            // 检查是否是日期格式异常
            if (isDateFormatException(e)) {
                log.warn("检测到日期格式异常，尝试使用原始JSON解析");
                return handleDateFormatException(authHeader, e);
            }

            return handleUndeclaredThrowableException(e);
            
        } catch (ApiException e) {
            // 重新抛出业务异常
            throw e;
            
        } catch (Exception e) {
            log.error("EpassportClient调用发生未知异常", e);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                "认证服务调用失败：" + e.getMessage());
        }
    }

    /**
     * 处理UndeclaredThrowableException
     */
    private Map<String,Object> handleUndeclaredThrowableException(UndeclaredThrowableException e) throws ApiException {
        Throwable cause = e.getCause();
        
        if (cause == null) {
            log.error("UndeclaredThrowableException没有根本原因");
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "认证服务调用异常");
        }
        
        log.error("UndeclaredThrowableException的根本原因：{}", cause.getClass().getSimpleName(), cause);
        
        // 处理网络相关异常

        
        // 处理HTTP相关异常

        
        // 处理JSON解析异常
        if (cause.getClass().getSimpleName().contains("JsonSyntaxException") ||
            cause.getClass().getSimpleName().contains("JsonParseException") ||
            cause.getClass().getSimpleName().contains("InvalidFormatException")) {

            String message = cause.getMessage();
            if (message != null && message.contains("Date")) {
                log.error("日期格式解析异常，服务器返回的日期格式不符合预期：{}", message);
                throw new ApiException(ApiTags.API_RESP_CODE_500100, "认证服务返回的日期格式异常");
            }

            throw new ApiException(ApiTags.API_RESP_CODE_500100, "认证服务响应格式异常");
        }
        
        // 处理其他运行时异常
        if (cause instanceof RuntimeException) {
            throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                "认证服务异常：" + cause.getMessage());
        }
        
        // 处理其他检查异常
        if (cause instanceof Exception) {
            throw new ApiException(ApiTags.API_RESP_CODE_500100, 
                "认证服务异常：" + cause.getMessage());
        }
        
        // 兜底处理
        throw new ApiException(ApiTags.API_RESP_CODE_500100, 
            "认证服务调用失败：" + cause.getMessage());
    }

    /**
     * 检查Epassport服务健康状态
     */
    public boolean isHealthy() {
        try {
            // 可以使用一个测试token或健康检查接口
            // 这里简化处理，实际应该调用健康检查接口
            return true;
        } catch (Exception e) {
            log.warn("Epassport服务健康检查失败", e);
            return false;
        }
    }

    /**
     * 验证token格式
     */
    public String validateAndFormatToken(String token) {

        
        String trimmedToken = token.trim();
        
        // 检查token长度

        
        // 添加Bearer前缀（如果没有）
        if (!trimmedToken.startsWith("Bearer ") && !trimmedToken.startsWith("bearer ")) {
            return "Bearer " + trimmedToken;
        }
        
        return trimmedToken;
    }

    /**
     * 检查是否是日期格式异常
     */
    private boolean isDateFormatException(UndeclaredThrowableException e) {
        Throwable cause = e.getCause();
        if (cause == null) {
            return false;
        }

        String className = cause.getClass().getSimpleName();
        String message = cause.getMessage();

        return className.contains("InvalidFormatException") &&
               message != null &&
               (message.contains("Date") || message.contains("date"));
    }

    /**
     * 将User对象转换为Map
     */
    private Map<String, Object> convertToMap(Object userObj) {
        if (userObj == null) {
            return null;
        }

        try {
            // 使用反射将对象转换为Map
            Map<String, Object> userMap = new java.util.HashMap<>();

            Class<?> clazz = userObj.getClass();
            java.lang.reflect.Field[] fields = clazz.getDeclaredFields();

            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(userObj);

                // 处理日期字段，转换为字符串
                if (value instanceof java.util.Date) {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    value = sdf.format((java.util.Date) value);
                }

                userMap.put(field.getName(), value);
            }

            log.debug("成功将User对象转换为Map，字段数量：{}", userMap.size());
            return userMap;

        } catch (Exception e) {
            log.error("转换User对象为Map失败", e);
            throw new RuntimeException("用户信息转换失败", e);
        }
    }

    /**
     * 处理日期格式异常
     */
    private Map<String, Object> handleDateFormatException(String authHeader, UndeclaredThrowableException e) throws ApiException {
        log.warn("尝试使用原始JSON方式处理日期格式异常");

        try {
            // 这里可以尝试直接获取原始JSON并手动解析
            // 由于我们无法直接获取原始响应，这里返回一个错误
            log.error("无法处理日期格式异常，原因：{}", e.getCause().getMessage());

            throw new ApiException(ApiTags.API_RESP_CODE_500100,
                "认证服务返回的用户信息包含不兼容的日期格式，已尝试多种解析方式均失败");

        } catch (ApiException ae) {
            throw ae;
        } catch (Exception ex) {
            log.error("处理日期格式异常时发生错误", ex);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "用户信息解析失败");
        }
    }
}
