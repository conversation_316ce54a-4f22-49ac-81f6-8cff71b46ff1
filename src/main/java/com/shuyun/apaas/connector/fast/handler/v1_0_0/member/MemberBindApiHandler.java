package com.shuyun.apaas.connector.fast.handler.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.member.MemberBindParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.MemberBindResult;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.response.BindResponse;
import com.shuyun.kylin.member.api.response.BindResult;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberBindApiHandler extends AbstractApiHandler<MemberBindParam, MemberBindResult, MemberBindParam, BindResult> {

    private final MemberService memberService;

    @Inject
    public MemberBindApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberBindParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.bothRequiredValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MemberBindParam beforeRequest(MemberBindParam param) {
        super.beforeRequest(param);
        param.setRegisterChannel(param.getRequestChannel());
        return param;
    }

    @Override
    public MemberBindParam prepareParam(MemberBindParam param) {
        return param;
    }

    @Override
    public BindResult request(MemberBindParam invokeParam) {
        return memberService.bind(invokeParam);
    }

    @Override
    public MemberBindResult prepareResult(MemberBindParam param, BindResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            MemberBindResult r = new MemberBindResult();
            BindResponse bindResponse = result.getData();
            r.setMemberId(bindResponse.getMemberId());
            r.setStatus(bindResponse.getStatus());
            return r;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500200, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_BIND;
    }
}
