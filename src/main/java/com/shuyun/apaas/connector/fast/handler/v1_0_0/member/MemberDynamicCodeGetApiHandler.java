package com.shuyun.apaas.connector.fast.handler.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.domain.DynamicCode;
import com.shuyun.fast.v1_0_0.param.member.MemberDynamicCodeGetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class MemberDynamicCodeGetApiHandler extends AbstractApiHandler<MemberDynamicCodeGetParam, DynamicCode, MemberDynamicCodeGetParam, DynamicCode> {

    private final MemberService memberService;

    @Inject
    public MemberDynamicCodeGetApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }


    @Override
    public void validate(MemberDynamicCodeGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MemberDynamicCodeGetParam beforeRequest(MemberDynamicCodeGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public MemberDynamicCodeGetParam prepareParam(MemberDynamicCodeGetParam param) {
        return param;
    }

    @Override
    public DynamicCode request(MemberDynamicCodeGetParam invokeParam) {
        return memberService.dynamicCodeGet(invokeParam);
    }

    @Override
    public DynamicCode prepareResult(MemberDynamicCodeGetParam param, DynamicCode result) {
        if(StringUtils.isNotEmpty(result.getDynamicCode())){
            return result;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500207, "get empty dynamic code");
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DYNAMICCODE_GET;
    }
}
