package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.fast.v1_0_0.domain.PointRecord;
import com.shuyun.fast.v1_0_0.param.point.*;
import jakarta.inject.Inject;

@Title("积分场景")
public class PointOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("积分查询")
    @Api(name = ApiTags.API_NAME_POINT_GET)
    public ApiResult<Point> get(@Parameter PointGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("积分明细查询")
    @Api(name = ApiTags.API_NAME_POINT_RECORDS_GET)
    public ApiResult<PageResult<PointRecord>> recordsGet(@Parameter PointRecordsGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("积分变更")
    @Api(name = ApiTags.API_NAME_POINT_MODIFY)
    public ApiResult<Void> modify(@Parameter PointModifyParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("积分冻结")
    @Api(name = ApiTags.API_NAME_POINT_FREEZE)
    public ApiResult<Void> freeze(@Parameter PointFreezeParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("消耗已冻结积分")
    @Api(name = ApiTags.API_NAME_POINT_FREEZE_DEDUCT)
    public ApiResult<Void> freezeDeduct(@Parameter PointFreezeDeductParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("积分解冻")
    @Api(name = ApiTags.API_NAME_POINT_UNFREEZE)
    public ApiResult<Void> unfreeze(@Parameter PointUnfreezeParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("积分交易撤销")
    @Api(name = ApiTags.API_NAME_POINT_REVERT)
    public ApiResult<Void> revert(@Parameter PointRevertParam param)  {
        return handlerRouter.route(param).handle(param);
    }

}
