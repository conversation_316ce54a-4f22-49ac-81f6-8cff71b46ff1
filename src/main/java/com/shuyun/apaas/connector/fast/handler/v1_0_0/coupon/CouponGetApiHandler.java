package com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.BenefitCache;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.BenefitService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponGetParam;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.ticket.benefit.domain.Benefit;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class CouponGetApiHandler extends AbstractApiHandler<CouponGetParam, CouponGetResult, CouponGetParam, BenefitResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;

    @Inject
    public CouponGetApiHandler(MemberService memberService,
                               BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponGetParam param) {
        super.validate(param);
    }

    @Override
    public CouponGetParam beforeRequest(CouponGetParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public CouponGetParam prepareParam(CouponGetParam param) {
        return param;
    }

    @Override
    public BenefitResponse request(CouponGetParam invokeParam) {
        return benefitService.couponGet(invokeParam);
    }

    @Override
    public CouponGetResult prepareResult(CouponGetParam param, BenefitResponse result) {

        if(Objects.nonNull(result)){
            BenefitCache cache = benefitService.bizCacheGet(param);
            Benefit benefit = result.getBenefit();
            BenefitProject project = benefitService.getProjectCache(cache.getProgramId(), benefit.getProjectId());
            CouponGetResult cr = JsonUtil.outPutConvert(benefit, CouponGetResult.class);
            cr.setMemberId(benefit.getHolder());
            cr.setDenomination(benefit.getDenomination());
            if(param.getShowProject()){
                cr.setProject(JsonUtil.outPutConvert(project, CouponProject.class));
            }
            //填充项目信息
            benefitService.fillProjectInfo(cr, project);
            //填充选择器数据
            benefitService.fillSelectorData(param, cr, project);
            // TODO: 2024/3/14 参数封装
            return cr;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500233, param.getCode());

    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GET;
    }
}
