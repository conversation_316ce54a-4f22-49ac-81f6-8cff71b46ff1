package com.shuyun.apaas.connector.fast.event.client;

import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.event.domain.v1_0_0.BenefitProjectEvent;
import com.shuyun.apaas.connector.fast.kafka.KafkaSender;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class BenefitProjectEventListener extends EsEventListener<BenefitProjectEvent> {

    @Inject
    public BenefitProjectEventListener(KafkaSender kafkaSender,
                                       FastConfig config){
        super(kafkaSender, config.getEventService(), 15, 10);
    }

    private String serviceId = "fast-connector";

    @Override
    public BenefitProjectEvent type() {
        return new BenefitProjectEvent();
    }

    @Override
    public String currentService() {
        return serviceId;
    }
}
