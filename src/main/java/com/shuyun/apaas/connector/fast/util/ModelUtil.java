package com.shuyun.apaas.connector.fast.util;

import com.shuyun.air.framework.model.AttributeSet;
import com.shuyun.apaas.connector.fast.constant.ModelTags;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.dm.api.domain.DataModel;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrderItem;
import com.shuyun.ticket.support.MetadataSupport;
import io.micronaut.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
public class ModelUtil {

    public static <T> String getFieldNames(String prefix, Class<T> clazz){
        Field[] fields = clazz.getDeclaredFields();
        String names = "";
        for(Field f:fields){
            if(!"extension".equals(f.getName())){
                String fieldName = StringUtils.isEmpty(prefix)?f.getName():prefix + "." + f.getName();
                names = String.join(",", names, fieldName);
            }
        }
        log.info("class :{} fields:{}", clazz, names.substring(1, names.length()));
        return names.substring(1, names.length());
    }

    public static void initModel(String suffix, Map<String, AttributeSet> extModels) {
        long st = System.currentTimeMillis();

        initDataModel(suffix, extModels);

        log.info("初始化模型结束，模型FQN后缀：{}，总耗时{}秒", suffix, (System.currentTimeMillis() - st) / 1000);
    }

    private static void initDataModel(String suffix, Map<String, AttributeSet> extModels) {
        log.info("开始初始化数据模型");
        Map<Class<?>, String> fqnMap = new LinkedHashMap<>();
        // 对象模型
        fqnMap.put(BizCache.class, ModelTags.DATA_FQN_FAST_BIZCACHE);
        Map<Class<?>, String> shardingFqnMap = new LinkedHashMap<>();
        // 父类映射关系
        Map<Class<?>, Class<?>> superMap = new LinkedHashMap<>();
        // 需要开启数据事件的模型
        Set<Class<?>> subscribed = new LinkedHashSet<>();
        // 使用模式
        Map<Class<?>, String> useModeMap = new LinkedHashMap<>();
        useModeMap.put(BizCache.class, "OLTP");

        Map<Class<?>, DataModel> models = MetadataSupport.scanModels(fqnMap, superMap);
        models.forEach((k, v) -> {
            if (shardingFqnMap.containsKey(k)) {
                v.setTitle(v.getTitle() + suffix);
            }
            if (subscribed.contains(k)) {
                v.setSubscribed(true);
            }
            if (useModeMap.containsKey(k)) {
                v.setUseMode(useModeMap.get(k));
            }
            v.setOwner("prctvmkt");

            if (null != v.getEnums() && !v.getEnums().isEmpty()) {
                // 枚举模型
                log.info("模型FQN：{}，枚举可选值数量：{}", v.getFqn(), v.getEnums().size());
                return;
            }

            int existsSize = v.getFields().size();
            // 基于属性集，补充自定义的属性
            MetadataSupport.withExtends(v, extModels.get(shardingFqnMap.getOrDefault(k, fqnMap.get(k))));
            int extSize = v.getFields().size() - existsSize;

            log.info("模型FQN：{}，预置属性数量：{}，自定义属性数量：{}", v.getFqn(), existsSize, extSize);
        });
        log.info("数据模型扫描完成，需初始化的数据模型数量为：{}", models.size());

        MetadataSupport.migrate(models.values());

        log.info("初始化数据模型完成");
    }

    public static void main(String[] args) {

//        ModelUtil.getFieldNames(TradeMainOrder.class);
        String fields = ModelUtil.getFieldNames("", TradeMainOrder.class).replace("orderItems", ModelUtil.getFieldNames("orderItems", TradeMainOrderItem.class));
        log.info("fields:{}", fields);
    }
}
