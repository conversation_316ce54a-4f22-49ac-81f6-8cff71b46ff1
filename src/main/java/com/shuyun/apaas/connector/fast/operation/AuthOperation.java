package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.model.http.param.auth.SsoLoginParam;
import com.shuyun.apaas.connector.fast.model.http.result.auth.SsoLoginResult;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import jakarta.inject.Inject;

/**
 * 认证场景操作类
 * 参考其他操作类实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Title("认证场景")
public class AuthOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("单点登录")
    @Api(name = "auth.sso.login")
    public ApiResult<SsoLoginResult> ssoLogin(@Parameter SsoLoginParam param) {
        return handlerRouter.route(param).handle(param);
    }
}
