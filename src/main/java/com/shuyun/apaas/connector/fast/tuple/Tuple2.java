package com.shuyun.apaas.connector.fast.tuple;

/**
 * @Author: xiaolong.chang
 * @Date: 2019/3/13
 * @Description:
 */
public class Tuple2<A, B> {
    public final A first;
    public final B second;

    public Tuple2(A a, B b) {
        first = a;
        second = b;
    }

    @Override
    public int hashCode(){
        return toString().hashCode();
    }
    @Override
    public boolean equals(Object obj){
        if(this == obj){
            return true;
        }
        if(obj == null || getClass() != obj.getClass()){
            return false;
        }
        Tuple2 other = (Tuple2)obj;
        if(first == null){
            return second.equals(other.second);
        }
        if(second == null){
            return first.equals(other.first);
        }
        return first.equals(other.first) && second.equals(other.second);
    }
    public String toString() {
        return "(" + first + ", " + second + ")";
    }
}
