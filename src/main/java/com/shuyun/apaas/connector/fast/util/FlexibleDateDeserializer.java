package com.shuyun.apaas.connector.fast.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 灵活的日期反序列化器
 * 支持多种日期格式
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class FlexibleDateDeserializer extends JsonDeserializer<Date> {

    private static final String[] DATE_FORMATS = {
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd'T'HH:mm:ss",
        "yyyy-MM-dd'T'HH:mm:ss.SSS",
        "yyyy-MM-dd'T'HH:mm:ss.SSSX",
        "yyyy-MM-dd'T'HH:mm:ssX",
        "yyyy-MM-dd'T'HH:mm:ss'Z'",
        "yyyy-MM-dd",
        "MM/dd/yyyy",
        "dd/MM/yyyy",
        "yyyy/MM/dd"
    };

    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String dateString = p.getText();
        
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        
        dateString = dateString.trim();
        
        // 尝试解析各种日期格式
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false);
                Date date = sdf.parse(dateString);
                log.debug("成功解析日期：{} 使用格式：{}", dateString, format);
                return date;
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        // 尝试解析时间戳
        try {
            long timestamp = Long.parseLong(dateString);
            Date date = new Date(timestamp);
            log.debug("成功解析时间戳：{}", dateString);
            return date;
        } catch (NumberFormatException e) {
            // 不是时间戳
        }
        
        // 所有格式都失败了
        log.warn("无法解析日期字符串：{}", dateString);
        throw new IOException("无法解析日期格式：" + dateString);
    }
}
