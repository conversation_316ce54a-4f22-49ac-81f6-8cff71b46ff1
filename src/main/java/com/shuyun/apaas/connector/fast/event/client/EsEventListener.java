package com.shuyun.apaas.connector.fast.event.client;

import com.shuyun.apaas.connector.fast.config.EventServiceConfig;
import com.shuyun.apaas.connector.fast.event.domain.v1_0_0.EsEvent;
import com.shuyun.apaas.connector.fast.kafka.KafkaSender;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.es.sdk.domain.EventConsumeResponse;
import com.shuyun.es.sdk.domain.Record;
import com.shuyun.es.sdk.factory.EventServiceSdkFactory;
import com.shuyun.es.sdk.service.EventService;
import lombok.extern.slf4j.Slf4j;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public abstract class EsEventListener<T extends EsEvent> {

    private EventService eventService;
    private KafkaSender kafkaSender;
    private EventServiceConfig eventServiceConfig;
    private long initialDelay;
    private long delay;

    abstract public T type();
    abstract public String currentService();
    public EsEventListener(KafkaSender kafkaSender,
                           EventServiceConfig eventServiceConfig,
                           long initialDelay,
                           long delay){
        this.kafkaSender = kafkaSender;
        this.eventServiceConfig = eventServiceConfig;
        this.initialDelay = initialDelay;
        this.delay = delay;
        register();
    }

    public void register(){
        eventService = EventServiceSdkFactory.byUrl(eventServiceConfig.getServerUrl(), eventServiceConfig.getCaller(), eventServiceConfig.getSecret());
        log.info("es event handler:{} register success", this.getClass().getName());
        final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleWithFixedDelay(()->consume(), initialDelay, delay, TimeUnit.SECONDS);
    }

    protected void consume(){
        try {
            log.debug("consume event from event service...");
            EsEvent event = type();
            EventConsumeResponse response = eventService.consume(event.fqn(), eventServiceConfig.getUserName(), eventServiceConfig.getUserSecret());
            List<Record> events = response.getData();
            if (response.isSuccess() && !events.isEmpty()) {
                log.info("fetch event response:{}", JsonUtil.serialize(response));
                List<EsEvent> list = events
                        .stream()
                        .map(m->JsonUtil.convert(m.getRecord(), event.getClass()))
                        .collect(Collectors.toList());
                for (EsEvent esEvent : list) {
                    esEvent.eventDispatcherService(currentService());
                    kafkaSender.send(esEvent.routeTopic(), esEvent.partitionKey(), esEvent);
                }
            }
        } catch (Exception e){
            log.error("consume event error:", e);
        }
    }
}
