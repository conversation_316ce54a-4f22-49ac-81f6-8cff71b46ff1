package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.cnc.api.ConfigAware;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import jakarta.inject.Singleton;
import lombok.Getter;
import org.jetbrains.annotations.Nullable;

public class ConfigService implements ConfigAware<FastConfig> {
    @Getter
    private FastConfig config;
    @Override
    public void setConfig(@Nullable FastConfig config) {
        this.config = config;
    }
}
