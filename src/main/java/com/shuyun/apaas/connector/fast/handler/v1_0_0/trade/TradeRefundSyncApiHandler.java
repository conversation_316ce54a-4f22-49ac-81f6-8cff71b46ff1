package com.shuyun.apaas.connector.fast.handler.v1_0_0.trade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.TradeCache;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.TradeService;
import com.shuyun.fast.v1_0_0.constant.OrderOwnerType;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_1.param.trade.TradeRefundSyncParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class TradeRefundSyncApiHandler extends AbstractApiHandler<TradeRefundSyncParam, Void, TradeRefundSyncParam, Void> {

    private final TradeService tradeService;
    private final MemberService memberService;

    @Inject
    public TradeRefundSyncApiHandler(TradeService tradeService,
                                     MemberService memberService){
        this.tradeService = tradeService;
        this.memberService = memberService;
    }

    @Override
    public String apiVersion() {
        return ApiTags.API_VERSION_1_0_1;
    }

    @Override
    public void validate(TradeRefundSyncParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }


    @Override
    public TradeRefundSyncParam beforeRequest(TradeRefundSyncParam param) {
        super.beforeRequest(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(StringUtils.isNotEmpty(identify.getMemberId())){//校验会员是否存在
            Boolean exist = memberService.memberExists(param, identify.getMemberId());
            param.getRefund().setOrderOwnerType(exist? OrderOwnerType.MEMBER:OrderOwnerType.CONSUMER);
        }else{
            try {
                param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
                param.getRefund().setOrderOwnerType(OrderOwnerType.MEMBER);
            } catch (ApiException e){
                param.getRefund().setOrderOwnerType(OrderOwnerType.CONSUMER);
                log.info("find no member...");
            }
        }
        TradeCache cache = tradeService.bizCacheGet(param);
        param.getRefund().setId(param.getRefund().getOrderId());
        param.getRefund().setTotalFee(param.getRefund().getPayment());
        param.getRefund().setMemberType(cache.getMemberType());
        param.getRefund().setChannelType(param.getRequestChannel());
        param.getRefund().getRefundOrderItems().forEach(item->{
            item.setId(item.getOrderItemId());
            item.setMemberType(cache.getMemberType());
            item.setOrderId(param.getRefund().getOrderId());
            item.setShopTypeCode(param.getRefund().getShopTypeCode());
            item.setShopCode(param.getRefund().getShopCode());
            item.setShopName(param.getRefund().getShopName());
            item.setTotalFee(item.getPayment());
            item.setOrderTime(param.getRefund().getOrderTime());
            item.setOriginOrderId(param.getRefund().getOriginOrderId());
            item.setChannelType(param.getRequestChannel());
            item.setOrderOwnerType(param.getRefund().getOrderOwnerType());
        });
        return param;
    }

    @Override
    public TradeRefundSyncParam prepareParam(TradeRefundSyncParam param) {
        // TODO: 2024/3/29 时区转换
        return param;
    }

    @Override
    public Void request(TradeRefundSyncParam invokeParam) {
        return tradeService.refundSync(invokeParam);
    }

    @Override
    public Void prepareResult(TradeRefundSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TRADE_REFUND_SYNC;
    }
}
