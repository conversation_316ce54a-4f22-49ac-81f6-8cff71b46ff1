package com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.BenefitService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.CouponProjectGetParam;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class CouponProjectGetApiHandler extends AbstractApiHandler<CouponProjectGetParam, CouponProject, CouponProjectGetParam, BenefitProject> {
    private final BenefitService benefitService;
    private final MemberService memberService;

    @Inject
    public CouponProjectGetApiHandler(MemberService memberService,
                                      BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponProjectGetParam param) {
        super.validate(param);
    }

    @Override
    public CouponProjectGetParam beforeRequest(CouponProjectGetParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public CouponProjectGetParam prepareParam(CouponProjectGetParam param) {
        return param;
    }

    @Override
    public BenefitProject request(CouponProjectGetParam invokeParam) {
        return benefitService.projectGet(invokeParam);
    }

    @Override
    public CouponProject prepareResult(CouponProjectGetParam param, BenefitProject result) {
        if(Objects.nonNull(result)){
            CouponProject cp = JsonUtil.outPutConvert(result, CouponProject.class);
            //填充选择器数据
            benefitService.fillSelectorData(param, cp, result);
            return cp;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500238, param.getProjectId());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_PROJECT_GET;
    }
}
