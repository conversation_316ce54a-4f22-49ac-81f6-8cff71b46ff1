package com.shuyun.apaas.connector.fast.handler.v1_0_0.activity;

import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.model.http.param.activity.ActivityPrizeRecordSyncParam;
import com.shuyun.apaas.connector.fast.service.v1_0_0.ActivityService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

/**
 * 活动领奖记录同步处理器
 * 参考门店同步接口实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Singleton
public class ActivityPrizeRecordSyncApiHandler extends AbstractApiHandler<ActivityPrizeRecordSyncParam, Void,ActivityPrizeRecordSyncParam,Void> {

    private final ActivityService activityService;

    @Inject
    public ActivityPrizeRecordSyncApiHandler(ActivityService activityService) {
        this.activityService = activityService;
    }

    @Override
    public void validate(ActivityPrizeRecordSyncParam param) {
        super.validate(param);
    }

    @Override
    public ActivityPrizeRecordSyncParam beforeRequest(ActivityPrizeRecordSyncParam param) {
        super.beforeRequest(param);
        // 设置记录ID，如果没有提供则使用活动ID+用户ID+邀请人ID+邀请时间的组合
        if (param.getId() == null || param.getId().trim().isEmpty()) {
            String recordId = generateRecordId(param);
            param.setId(recordId);
        }
        // 设置渠道类型
        return param;
    }

    @Override
    public ActivityPrizeRecordSyncParam prepareParam(ActivityPrizeRecordSyncParam param) {
        // TODO: 时区转换
        return param;
    }

    @Override
    public Void request(ActivityPrizeRecordSyncParam invokeParam) {
        return activityService.prizeRecordSync(invokeParam);
    }

    @Override
    public Void prepareResult(ActivityPrizeRecordSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return "activity.prizeRecord.sync";
    }

    /**
     * 生成记录ID
     * 使用活动ID+用户ID+邀请人ID+邀请时间戳的组合
     */
    private String generateRecordId(ActivityPrizeRecordSyncParam param) {
        long timestamp = param.getInviteTime() != null ? 
            param.getInviteTime().toEpochSecond(java.time.ZoneOffset.UTC) : 
            System.currentTimeMillis() / 1000;
        
        return String.format("%s_%s_%s_%d", 
            param.getActId(), 
            param.getUserId(), 
            param.getInviterId(), 
            timestamp);
    }
}
