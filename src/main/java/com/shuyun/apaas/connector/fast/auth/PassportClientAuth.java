package com.shuyun.apaas.connector.fast.auth;

import com.shuyun.lite.client.Passport;
import com.shuyun.lite.client.PassportClientFactory;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Singleton
public class PassportClientAuth {

    @Inject
    public PassportClientAuth(){
    }

    public void register(String clientId) {
        if(StringUtils.isNotEmpty(clientId)){
            Passport passport = PassportClientFactory.instance();
            Map<String, Object> body = new HashMap<>();
            body.put("clientId", clientId);
            body.put("clientName", clientId);
            body.put("version", 2);
            String secret = passport.registerClient(body);
            log.info("服务:{}注册后返回secret:{}", clientId, secret);
        }else{
            log.info("passport.clientId参数未配置,不注册");
        }

    }
}
