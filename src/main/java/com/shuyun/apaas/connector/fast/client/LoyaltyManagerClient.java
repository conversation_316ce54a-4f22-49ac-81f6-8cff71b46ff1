package com.shuyun.apaas.connector.fast.client;

import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.fast.loyalty.FSMPointEvent;
import com.shuyun.fast.loyalty.PCStatus;
import com.shuyun.fast.loyalty.PointSortType;
import com.shuyun.fast.loyalty.SortType;
import com.shuyun.loyalty.api.request.BudgetGradeRequest;
import com.shuyun.loyalty.api.response.BudgetGradeResponse;
import com.shuyun.loyalty.api.response.MemberMedalResponse;
import com.shuyun.loyalty.sdk.api.model.GradeRecordType;
import com.shuyun.loyalty.sdk.api.model.Page;
import com.shuyun.loyalty.sdk.api.model.http.grade.GradeRuleGroupApiVo;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import com.shuyun.loyalty.sdk.api.model.http.medal.MedalRuleGroupApiVo;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordResponse;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberValidPointResponse;
import retrofit2.http.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import javax.annotation.Nullable;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public interface LoyaltyManagerClient {
    @Operation(summary = "获取会员等级")
    @GET("/loyalty-manager/v1/open/grade/member")
    List<MemberGradeResponse> findMemberGradeList(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number")Integer number,
                                                  @Parameter(description = "每页记录数 ", example = "20") @Query(value = "pageSize")Integer pageSize,
                                                  @Parameter(description = "等级体系id" ,required = true) @Query(value = "gradeHierarchyId") @NotNull Long gradeHierarchyId,
                                                  @Parameter(description = "会员ID") @Query(value = "memberId")String memberId,
                                                  @Parameter(description = "当前等级ID") @Query(value = "currentGradeDefinitionId") @Nullable Long currentGradeDefinitionId);

    @Operation(summary = "获取会员等级记录")
    @GET("/loyalty-manager/v1/open/grade/member/record")
    List<MemberGradeRecordResponse> findMemberGradeRecords(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number") Integer number,
                                                           @Parameter(description = "每页记录数 ", example = "20") @Query(value = "pageSize") Integer pageSize,
                                                           @Parameter(description = "等级体系id" ,required = true) @Query(value = "gradeHierarchyId") @NotNull Long gradeHierarchyId,
                                                           @Parameter(description = "会员ID") @Query(value = "memberId") String memberId,
                                                           @Parameter(description = "channelType") @Query(value = "channelType") @Nullable String channelType,
                                                           @Parameter(description = "排序") @Query(value = "sortType") SortType sortType,
                                                           @Parameter(description = "变更类型") @Query(value = "recordType") @Nullable GradeRecordType recordType,
                                                           @Parameter(description = "变更前等级ID") @Query(value = "orignalGradeId") @Nullable Long orignalGradeId,
                                                           @Parameter(description = "变更后等级ID") @Query(value = "currentGradeId") @Nullable Long currentGradeId,
                                                           @Parameter(description = "变更筛选开始时间,ISO8601格式") @Query(value = "startTime") String startTime,
                                                           @Parameter(description = "变更筛选结束时间,ISO8601格式") @Query(value = "endTime") String endTime);




    @Operation(summary = "分页获取会员等级记录")
    @GET("/loyalty-manager/v1/open/grade/member/pageRecord")
    Page<MemberGradeRecordResponse> pageMemberGradeRecordList(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number")Integer number,
                                                              @Parameter(description = "每页记录数 ", example = "20") @Query(value = "pageSize")Integer pageSize,
                                                              @Parameter(description = "等级体系id" ,required = true) @Query(value = "gradeHierarchyId")Long gradeHierarchyId,
                                                              @Parameter(description = "会员ID") @Query(value = "memberId")String memberId,
                                                              @Parameter(description = "channelType") @Query(value = "channelType") @Nullable String channelType,
                                                              @Parameter(description = "排序") @Query(value = "sortType") SortType sortType,
                                                              @Parameter(description = "变更类型") @Query(value = "recordType") @Nullable GradeRecordType recordType,
                                                              @Parameter(description = "变更前等级ID") @Query(value = "orignalGradeId") @Nullable Long orignalGradeId,
                                                              @Parameter(description = "变更后等级ID") @Query(value = "currentGradeId") @Nullable Long currentGradeId,
                                                              @Parameter(description = "变更筛选开始时间,ISO8601格式") @Query(value = "startTime") String startTime,
                                                              @Parameter(description = "变更筛选结束时间,ISO8601格式") @Query(value = "endTime")String endTime);

    @Operation(summary = "获取会员积分记录")
    @GET("/loyalty-manager/v1/open/point/member/record")
    List<MemberPointRecordResponse> findMemberPointRecords(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number")Integer number,
                                                           @Parameter(description = "每页记录数 ", example = "20") @Query(value = "pageSize")Integer pageSize,
                                                           @Parameter(description = "积分账户id", required = true) @Query(value = "pointAccountId") @NotNull Long pointAccountId,
                                                           @Parameter(description = "触发动作") @Query(value = "recordType") @Nullable FSMPointEvent recordType,
                                                           @Parameter(description = "批量触发动作") @Query(value = "recordTypes") @Nullable List<FSMPointEvent> recordTypes,
                                                           @Parameter(description = "状态") @Query(value = "status") @Nullable List<PCStatus> status,
                                                           @Parameter(description = "会员ID") @Query(value = "memberId")String memberId,
                                                           @Parameter(description = "店铺ID") @Query(value = "shopId") @Nullable String shopId,
                                                           @Parameter(description = "channelType") @Query(value = "channelType") @Nullable String channelType,
                                                           @Parameter(description = "变更筛选开始时间,ISO8601格式") @Query(value = "startTime")String startTime,
                                                           @Parameter(description = "变更筛选结束时间,ISO8601格式") @Query(value = "endTime")String endTime,
                                                           @Parameter(description = "原单ID") @Query(value = "traceId") @Nullable String traceId,
                                                           @Parameter(description = "排序") @Query(value = "sortType") @NotNull PointSortType sortType,
                                                           @Parameter(description = "变更方式") @Query(value = "changeMode") @Nullable String changeMode);

    @Operation(summary = "获取即将到期积分")
    @GET("/loyalty-manager/v1/open/point/member/valid")
    List<MemberValidPointResponse> findMemberValidPointList(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number")Integer number,
                                                            @Parameter(description = "每页记录数 ", example = "100") @Query(value = "pageSize")Integer pageSize,
                                                            @Parameter(description = "积分账户id", required = true) @Query(value = "pointAccountId") @NotNull Long pointAccountId,
                                                            @Parameter(description = "会员ID", required = true) @Query(value = "memberId")@NotEmpty String memberId,
                                                            @Parameter(description = "单位:YEAR,MONTH,DAY", required = true) @Query(value = "timeUnit") @NotNull TimeUnit timeUnit,
                                                            @Parameter(description = "值", required = true) @Query(value = "timeValue")@NotNull TimeUnit timeValue);
    @Operation(summary = "等级规则元数据")
    @Api
    @GET("/loyalty-manager/v1/open/grade/gradeRuleGroup/list/{gradeHierarchyId}?withI18nPayload=true")
    List<GradeRuleGroupApiVo> gradeRuleMetadata(@Parameter(description = "勋章体系id", required = true) @Path(value = "gradeHierarchyId") @NotNull Long gradeHierarchyId);

    @Operation(summary = "勋章规则元数据")
    @GET("/loyalty-manager/v1/open/medal/medalRuleGroup/list/{medalHierarchyId}?withI18nPayload=true")
    List<MedalRuleGroupApiVo> medalRuleMetadata(@Parameter(description = "勋章体系id", required = true) @Path(value = "medalHierarchyId") @NotNull Long medalHierarchyId);

    @Operation(summary = "查询会员勋章")
    @GET("/loyalty-manager/v1/open/medal/member")
    List<MemberMedalResponse> memberMedals(@Parameter(description = "当前页,从0开始", example = "0") @Query(value = "number")Integer number,
                                           @Parameter(description = "每页记录数 ", example = "100") @Query(value = "pageSize")Integer pageSize,
                                           @Parameter(description = "会员ID", required = true) @Query(value = "memberId")@NotEmpty String memberId,
                                           @Parameter(description = "计划ID", required = true) @Query(value = "planId") @NotNull Long planId,
                                           @Parameter(description = "勋章体系ID", required = true) @Query(value = "medalHierarchyId")@NotNull Long medalHierarchyId);

    @Operation(summary = "预算等级")
    @POST("/loyalty-manager/v1/open/grade/member/budget")
    ArrayList<BudgetGradeResponse> gradeBudget(@Valid @Body BudgetGradeRequest request);

}
