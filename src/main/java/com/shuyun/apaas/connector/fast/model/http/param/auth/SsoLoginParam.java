package com.shuyun.apaas.connector.fast.model.http.param.auth;

import com.shuyun.fast.base.ApiBaseParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 单点登录参数类
 * 参考门店同步接口实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@Schema(title = "单点登录参数")
public class SsoLoginParam extends ApiBaseParam {

    @Schema(title = "访问令牌", required = true, maxLength = 512, description = "用户访问令牌，用于验证用户身份")
    @NotBlank(message = "访问令牌不能为空")
    private String token;

    @Override
    public String apiName() {
        return "auth.sso.login";
    }
}
