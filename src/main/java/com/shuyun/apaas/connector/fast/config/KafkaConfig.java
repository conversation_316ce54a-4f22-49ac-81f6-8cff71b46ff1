package com.shuyun.apaas.connector.fast.config;

import com.shuyun.apaas.cnc.api.annotations.media.Schema;
import lombok.Data;

@Schema(title = "kafka配置")
@Data
public class KafkaConfig {
    @Schema(title = "kafka broker地址:默认从配置中心读取,可单独配置")
    private String address;
    @Schema(title = "kafka消费组Id(默认值:fast-connector)", defaultValue = "fast-connector")
    private String groupId = "fast-connector";
    @Schema(title = "kafka offset reset策略(默认值:latest)", defaultValue = "latest")
    private String offsetReset = "latest";
    @Schema(title = "max.poll.records 每次最多拉取多少条记录(默认值:10)", defaultValue = "10")
    private String maxPollRecords = "10";
}
