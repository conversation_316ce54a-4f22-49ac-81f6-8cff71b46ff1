package com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.BenefitService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.coupon.CouponDiscountCalcParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.result.CouponDiscountCalcResult;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitDiscountCalcRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitDiscountCalcResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class CouponDiscountCalcApiHandler extends AbstractApiHandler<CouponDiscountCalcParam, CouponDiscountCalcResult, BenefitDiscountCalcRequest, BenefitDiscountCalcResponse> {
    private final BenefitService benefitService;
    private final MemberService memberService;

    @Inject
    public CouponDiscountCalcApiHandler(MemberService memberService,
                                        BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponDiscountCalcParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public CouponDiscountCalcParam beforeRequest(CouponDiscountCalcParam param) {
        super.beforeRequest(param);
        if(Objects.nonNull(param.getIdentify())){
            param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        }
        return param;
    }

    @Override
    public BenefitDiscountCalcRequest prepareParam(CouponDiscountCalcParam param) {
        BenefitDiscountCalcRequest request = benefitService.setSceneParam(new BenefitDiscountCalcRequest(), param);
        if(Objects.nonNull(param.getIdentify())){
            request.setUser(param.getIdentify().getMemberId());
        }
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        request.setShop(param.getShopCode());
        request.setPlatform(param.getPlatformId());
        request.setLockTransactionId(param.getLockTransactionId());
        request.setOrder(param.getOrder());
        return request;
    }

    @Override
    public BenefitDiscountCalcResponse request(BenefitDiscountCalcRequest invokeParam) {
        return benefitService.discountCalc(invokeParam);
    }

    @Override
    public CouponDiscountCalcResult prepareResult(CouponDiscountCalcParam param, BenefitDiscountCalcResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            CouponDiscountCalcResult calcResult = new CouponDiscountCalcResult();
            calcResult.setOrder(result.getOrder());
            calcResult.setDiscountResults(result.getDiscountResults());
            return calcResult;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500232, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_DISCOUNT_CALC;
    }
}
