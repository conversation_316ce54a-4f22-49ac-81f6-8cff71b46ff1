package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.model.http.param.activity.ActivityPrizeRecordSyncParam;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import jakarta.inject.Inject;

/**
 * 活动场景操作类
 * 参考MDM操作类实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Title("活动场景")
public class ActivityOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("活动领奖记录同步")
    @Api(name = "activity.prizeRecord.sync")
    public ApiResult<Void> prizeRecordSync(@Parameter ActivityPrizeRecordSyncParam param) {
        return handlerRouter.route(param).handle(param);
    }
}
