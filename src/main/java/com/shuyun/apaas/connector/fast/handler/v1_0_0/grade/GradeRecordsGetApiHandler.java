package com.shuyun.apaas.connector.fast.handler.v1_0_0.grade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.GradeService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.GradeRecord;
import com.shuyun.fast.v1_0_0.param.grade.GradeRecordsGetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.loyalty.sdk.api.model.Page;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse;
import com.shuyun.ticket.util.JsonUtil;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Singleton
public class GradeRecordsGetApiHandler extends AbstractApiHandler<GradeRecordsGetParam, PageResult<GradeRecord>, GradeRecordsGetParam, Page<MemberGradeRecordResponse>> {

    private final GradeService gradeService;
    private final MemberService memberService;

    @Inject
    public GradeRecordsGetApiHandler(MemberService memberService,
                                     GradeService gradeService){
        this.memberService = memberService;
        this.gradeService = gradeService;
    }

    @Override
    public void validate(GradeRecordsGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public GradeRecordsGetParam beforeRequest(GradeRecordsGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public GradeRecordsGetParam prepareParam(GradeRecordsGetParam param) {
        return param;
    }

    @Override
    public Page<MemberGradeRecordResponse> request(GradeRecordsGetParam invokeParam) {
        return gradeService.gradeRecordsGet(invokeParam);
    }

    @Override
    public PageResult<GradeRecord> prepareResult(GradeRecordsGetParam param, Page<MemberGradeRecordResponse> result) {
        List<MemberGradeRecordResponse> content = result.getContent();
        List<GradeRecord> records = new ArrayList<>();
        // TODO: 2024/3/15 时区转换
        content.forEach(r->{
            GradeRecord record = JsonUtil.copyValue(r, GradeRecord.class);
            record.setChangeTime(DateUtil.localTime(r.getCreated()));
            record.setOriginalEffectTime(DateUtil.localTime(r.getOriginalEffectDate()));
            record.setOriginalExpireTime(DateUtil.localTime(r.getOriginalOverdueDate()));
            record.setCurrentEffectTime(DateUtil.localTime(r.getCurrentEffectDate()));
            record.setCurrentEffectTime(DateUtil.localTime(r.getCurrentOverdueDate()));
            records.add(record);
        });
        PageResult<GradeRecord> pageResult = new PageResult<>();
        pageResult.setPage(result.getNumber());
        pageResult.setPageSize(result.getSize());
        pageResult.setTotalPage(result.getTotalPages().intValue());
        pageResult.setTotalCount(result.getTotalElements());
        pageResult.setItems(records);
        return pageResult;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_RECORDS_GET;
    }
}
