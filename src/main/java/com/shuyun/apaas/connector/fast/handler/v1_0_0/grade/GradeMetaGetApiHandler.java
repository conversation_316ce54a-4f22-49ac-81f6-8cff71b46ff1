package com.shuyun.apaas.connector.fast.handler.v1_0_0.grade;

import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.GradeService;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.grade.GradeMetadataParam;
import com.shuyun.fast.v1_0_0.result.GradeMetadataResult;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Singleton
public class GradeMetaGetApiHandler extends AbstractApiHandler<GradeMetadataParam, List<GradeMetadataResult>, GradeMetadataParam, List<GradeMetadataResult>> {
    private final GradeService gradeService;

    @Inject
    public GradeMetaGetApiHandler(GradeService gradeService){
        this.gradeService = gradeService;
    }

    @Override
    public void validate(GradeMetadataParam param) {
        super.validate(param);
    }

    @Override
    public GradeMetadataParam beforeRequest(GradeMetadataParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public GradeMetadataParam prepareParam(GradeMetadataParam param) {
        return param;
    }

    @Override
    public List<GradeMetadataResult> request(GradeMetadataParam invokeParam) {
        return gradeService.gradeMetadataGet(invokeParam);
    }

    @Override
    public List<GradeMetadataResult> prepareResult(GradeMetadataParam param, List<GradeMetadataResult> result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_METADATA_GET;
    }
}
