package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.v1_0_0.domain.Grade;
import com.shuyun.fast.v1_0_0.domain.GradeRecord;
import com.shuyun.fast.v1_0_0.param.grade.GradeBudgetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeGetParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeMetadataParam;
import com.shuyun.fast.v1_0_0.param.grade.GradeRecordsGetParam;
import com.shuyun.fast.v1_0_0.result.GradeBudgetResult;
import com.shuyun.fast.v1_0_0.result.GradeMetadataResult;
import jakarta.inject.Inject;

import java.util.List;

@Title("等级场景")
public class GradeOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("等级查询")
    @Api(name = ApiTags.API_NAME_GRADE_GET)
    public ApiResult<Grade> get(@Parameter GradeGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("等级预算")
    @Api(name = ApiTags.API_NAME_GRADE_BUDGET)
    public ApiResult<GradeBudgetResult> budget(@Parameter GradeBudgetParam param){
        return handlerRouter.route(param).handle(param);
    }

    @Operation("等级明细查询")
    @Api(name = ApiTags.API_NAME_GRADE_RECORDS_GET)
    public ApiResult<PageResult<GradeRecord>> recordsGet(@Parameter GradeRecordsGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("等级/勋章元数据(规则可选)")
    @Api(name = ApiTags.API_NAME_GRADE_METADATA_GET)
    public ApiResult<List<GradeMetadataResult>> metadataGet(@Parameter GradeMetadataParam param){
        return handlerRouter.route(param).handle(param);
    }

}
