package com.shuyun.apaas.connector.fast.event.client;

import com.shuyun.apaas.connector.fast.config.EventServiceConfig;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.es.sdk.domain.Event;
import com.shuyun.es.sdk.domain.EventSendResponse;
import com.shuyun.es.sdk.factory.EventServiceSdkFactory;
import com.shuyun.es.sdk.service.EventService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Singleton
@Slf4j
public class EsEventSender {


    private EventService eventService;

    @Inject
    public EsEventSender(FastConfig config) {
        EventServiceConfig eventServiceConfig = config.getEventService();
        eventService = EventServiceSdkFactory.byUrl(eventServiceConfig.getServerUrl(), eventServiceConfig.getCaller(), eventServiceConfig.getSecret());
        log.info("es event sender:{} register success", this.getClass().getName());
    }


    public <T> EventSendResponse send(String fqn, T event){
        List<Map<String, Object>> data = Collections.singletonList(JsonUtil.convert(event, Map.class));
        Event e = new Event();
        e.setData(data);
        return eventService.sendEvent(fqn, e);
    }

}
