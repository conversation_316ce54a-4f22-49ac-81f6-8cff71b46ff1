package com.shuyun.apaas.connector.fast.interceptor;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.apaas.cnc.api.annotations.aop.Advice;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.fast.base.ApiBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import java.util.Objects;

@Advice(Api.class)
@Slf4j
public class ApiInterceptor implements MethodInterceptor {
    final private ObjectMapper objectMapper = new ObjectMapper();

    public ApiInterceptor(){
        SimpleFilterProvider defaultProvider = new SimpleFilterProvider();
        SimpleBeanPropertyFilter defaultFilter = SimpleBeanPropertyFilter.serializeAll();
        defaultProvider.addFilter("tradeOrderFilter", defaultFilter);
        defaultProvider.addFilter("tradeOrderItemFilter", defaultFilter);
        defaultProvider.addFilter("tradeRefundFilter", defaultFilter);
        defaultProvider.addFilter("tradeRefundOrderItemFilter", defaultFilter);
        defaultProvider.addFilter("memberModifyParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmOrgSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmProductSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("mdmShopSyncParamFilter", defaultFilter);
        defaultProvider.addFilter("activityPrizeRecordSyncParamFilter", defaultFilter);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setFilterProvider(defaultProvider);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        String apiPath = "";
        Object param = invocation.getArguments()[0];
        String traceId = "";
        if(Objects.nonNull(param) && param instanceof ApiBaseParam){
            traceId = ((ApiBaseParam) param).getTransactionId();
            apiPath = ((ApiBaseParam) param).apiName();
        }
        try {
            log.info("api path:{} traceId:{} api request:{}", apiPath, traceId, objectMapper.writeValueAsString(param));
        } catch (Exception e){
            log.error("error:", e);
        }

        long startTime = System.currentTimeMillis();
        Object result = invocation.proceed();
        try {
            log.info("api path:{} traceId:{} api response:{}", apiPath, traceId, objectMapper.writeValueAsString(result));
        } catch (Exception e){
            log.error("error:", e);
        }
        long duration = System.currentTimeMillis() - startTime;
        log.info("api 方法:{} 执行时间:{}ms", apiPath, duration);
        return result;
    }
}
