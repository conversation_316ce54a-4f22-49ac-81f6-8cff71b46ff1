package com.shuyun.apaas.connector.fast.service.v1_0_0;
import com.shuyun.apaas.connector.fast.cache.CacheManager;
import com.shuyun.apaas.connector.fast.cache.SyncCache;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.*;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.apaas.connector.fast.repository.BizCacheRepository;
import com.shuyun.ticket.util.JsonUtil;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Singleton
@Slf4j
public class BizCacheService {

    private final BizCacheRepository bizCacheRepository;
    private final SyncCache bizCache;

    private Map<String, Class> cacheTypes = new ConcurrentHashMap();

    @Inject
    public BizCacheService(CacheManager cacheManager,
                           BizCacheRepository bizCacheRepository){
        this.bizCacheRepository = bizCacheRepository;
        this.bizCache = cacheManager.getCache("bizCache");
        init();
    }

    public void init(){
        cacheTypes.put("benefit", BenefitCache.class);
        cacheTypes.put("grade", GradeCache.class);
        cacheTypes.put("mbsp", MbspCache.class);
        cacheTypes.put("mdm", MdmCache.class);
        cacheTypes.put("point", PointCache.class);
        cacheTypes.put("selector", SelectorCache.class);
        cacheTypes.put("tag", TagCache.class);
        cacheTypes.put("trade", TradeCache.class);

        Iterable<BizCache> caches = bizCacheRepository.findAll();
        Iterator<BizCache> iterator = caches.iterator();
        while(iterator.hasNext()){
            BizCache cache = iterator.next();
            String tenantId = cache.getTenantId();
            String bizCode = cache.getBizCode();
            String cacheType = cache.getCacheType();
            Class<BaseCache> clazz = cacheTypes.get(cacheType);
            List<BaseCache> list = load(clazz, tenantId, bizCode, cacheType);
            if(CollectionUtils.isNotEmpty(list)){
                String key = String.join("-", tenantId, bizCode, cacheType);
                bizCache.put(key, list);
            }
        }
        log.info("init biz cache success");
    }

    public <T extends BaseCache> List<T> load(Class<T> clazz, String tenantId, String bizCode, String cacheType){
        log.info("load cache from db,tenantId:{}...bizCode:{}...cacheType:{}", tenantId,bizCode,cacheType);
        String cacheContent;
        BizCache cache = bizCacheRepository.find(tenantId, bizCode, cacheType).orElse(null);
        if(Objects.nonNull(cache)){
            cacheContent = cache.getValue();
            return JsonUtil.listFromJson(cacheContent, clazz);
        }
        return Collections.emptyList();
    }

    public <T extends BaseCache> List<T> get(Class<T> clazz, String tenantId, String bizCode, String cacheType){
        String key = String.join("-", tenantId, bizCode, cacheType);
        List<T> cache = (List<T>)bizCache.get(key);
        if(Objects.nonNull(cache)){
            return cache;
        }else{
            List<T> list = load(clazz, tenantId, bizCode, cacheType);
            if(CollectionUtils.isNotEmpty(list)){
                bizCache.put(key, list);
            }
            return list;
        }
    }

    /**
     * 1、重载缓存
     * 2、更新缓存
     * @param tenantId
     * @param bizCode
     * @param cacheType
     * @param <T>
     */
    public <T extends BaseCache> void refresh(String tenantId, String bizCode, String cacheType){
        Class clazz = cacheTypes.get(cacheType);
        String key = String.join("-", tenantId, bizCode, cacheType);
        List<T> cache = load(clazz, tenantId, bizCode, cacheType);
        if(!cache.isEmpty()){
            bizCache.put(key, cache);
        }
    }
}
