package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.config.DataapiConfig;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.dm.api.vo.FetchStartRequest;
import com.shuyun.dm.api.vo.FetchStartResponse;
import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.dm.sdk.Options;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.function.Function;

@Slf4j
@Singleton
public class DataService {

    @Getter
    private DataapiHttpSdk dataapiHttpSdk;

    private DataapiConfig dataapiConfig;

    @Inject
    public DataService(FastConfig config){
        dataapiConfig = config.getDataapi();
        Options options = new Options.Builder()
                .tenantId(config.getEnv())
                .caller(config.getCallerKey())
                .secret(config.getCallerSecret())
                .appKey(config.getCallerKey())
                .appSecret(config.getCallerSecret())
                .version("v1")
                .enableSign(true)
                .build();
        dataapiHttpSdk = DataapiSdkFactory.INSTANCE.createDataapiHttpSdk(dataapiConfig.getDataApiServerUrl(), options, "default");
    }

    public void streamQuery(String sql, Map<String, Object> params, int fetchSize, Long totalSizeLimit, Function<List<Map<String,Object>>,Void> consumer) {
        if (params == null)
            params = new HashMap<>();
        if(Objects.isNull(totalSizeLimit)){
            totalSizeLimit = Long.MAX_VALUE;
        }
        FetchStartRequest request = new FetchStartRequest(sql.toString(), params, fetchSize);
        try (FetchStartResponse response = dataapiHttpSdk.fetch(request)) {
            List<Map<String,Object>> data;
            long totalSize = 0;
            do {
                data = response.next().getData();
                if (!CollectionUtils.isEmpty(data)) {
                    totalSize += data.size();
                    if(totalSize > totalSizeLimit){
                        log.info("sql:{} data size reach limited size:{}", sql, totalSizeLimit);
                        return ;
                    }
                    consumer.apply(data);
                }
            } while (data != null && data.size() > 0);
        } catch (Exception e) {
            log.error("stream query data error:", e);
        }
    }

}
