package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.model.http.param.tag.TagTaggingParam;
import com.shuyun.apaas.connector.fast.model.http.param.tag.TagUntaggingParam;
import com.shuyun.apaas.connector.fast.model.http.result.tag.OpenTagContent;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.cdp.tags.vo.category.OpenCategoryVo;
import com.shuyun.cdp.tags.vo.openapi.OpenTagVo;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.v1_0_0.param.tag.*;
import jakarta.inject.Inject;
import java.util.List;

@Title("标签场景")
public class TagOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("标签目录列表")
    @Api(name = ApiTags.API_NAME_TAG_CATEGORY_LIST)
    public ApiResult<List<OpenCategoryVo>> categoryList(@Parameter TagCategoryListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("标签列表")
    @Api(name = ApiTags.API_NAME_TAG_LIST)
    public ApiResult<PageResult<OpenTagVo>> list(@Parameter TagListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("标签打标")
    @Api(name = ApiTags.API_NAME_TAG_TAGGING)
    public ApiResult<Void> tagging(@Parameter TagTaggingParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("标签去标")
    @Api(name = ApiTags.API_NAME_TAG_UNTAGGING)
    public ApiResult<Void> untagging(@Parameter TagUntaggingParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("标签下用户列表")
    @Api(name = ApiTags.API_NAME_TAG_USER_LIST)
    public ApiResult<PageResult<String>> userList(@Parameter TagUserListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("用户标签列表")
    @Api(name = ApiTags.API_NAME_TAG_USER_TAGS)
    public ApiResult<List<OpenTagContent>> userTags(@Parameter TagUserTagsParam param)  {
        return handlerRouter.route(param).handle(param);
    }
}
