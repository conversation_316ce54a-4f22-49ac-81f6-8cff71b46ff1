package com.shuyun.apaas.connector.fast.handler.v1_0_0.grade;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.GradeService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.Grade;
import com.shuyun.fast.v1_0_0.param.grade.GradeGetParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
public class GradeGetApiHandler extends AbstractApiHandler<GradeGetParam, Grade, GradeGetParam, MemberGradeResponse> {
    private final GradeService gradeService;
    private final MemberService memberService;

    @Inject
    public GradeGetApiHandler(MemberService memberService,
                              GradeService gradeService){
        this.memberService = memberService;
        this.gradeService = gradeService;
    }

    @Override
    public void validate(GradeGetParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public GradeGetParam beforeRequest(GradeGetParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public GradeGetParam prepareParam(GradeGetParam param) {
        return param;
    }

    @Override
    public MemberGradeResponse request(GradeGetParam invokeParam) {
        return gradeService.gradeGet(invokeParam);
    }

    @Override
    public Grade prepareResult(GradeGetParam param, MemberGradeResponse result) {
        Grade grade = new Grade();
        grade.setMemberId(result.getMemberId());
        grade.setId(result.getCurrentGradeDefinitionId());
        grade.setName(result.getGradeDefinitionName());
        grade.setEffectTime(DateUtil.localTime(result.getEffectDate()));
        grade.setExpiredTime(DateUtil.localTime(result.getOverdueDate()));
        return grade;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_GRADE_GET;
    }
}
