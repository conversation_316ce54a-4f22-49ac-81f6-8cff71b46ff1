package com.shuyun.apaas.connector.fast.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class RequestLoggingInterceptor implements Interceptor {
    private final Boolean logEnable;
    public RequestLoggingInterceptor(Boolean logEnable){
        this.logEnable = logEnable;
    }

    @NotNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        if(logEnable){
            RequestBody requestBody = request.body();
            if (requestBody != null) {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                log.info("request body:{}", buffer.readString(StandardCharsets.UTF_8));
            }
            Response response = chain.proceed(request);
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                String bodyString = responseBody.string();
                log.info("response body:{}", bodyString);
                // 重建response，因为body只能被消费一次
                return response.newBuilder()
                        .body(ResponseBody.create(responseBody.contentType(), bodyString))
                        .build();
            }
            return response;
        }else{
            return chain.proceed(request);
        }

    }
}
