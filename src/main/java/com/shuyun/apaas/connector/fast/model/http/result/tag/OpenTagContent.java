package com.shuyun.apaas.connector.fast.model.http.result.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(
        description = "对外标签内容"
)
@Data
public class OpenTagContent {

    @Schema(
            description = "标签id"
    )
    private String tagId;
    @Schema(
            description = "标签名称"
    )
    private String name;
    @Schema(
            description = "多值标签值"
    )
    private List<String> tagValues;
}
