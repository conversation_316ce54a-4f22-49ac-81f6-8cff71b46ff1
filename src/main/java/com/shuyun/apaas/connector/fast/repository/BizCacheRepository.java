package com.shuyun.apaas.connector.fast.repository;

import com.shuyun.apaas.connector.fast.constant.ModelTags;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.apaas.connector.fast.service.v1_0_0.DataService;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.dm.api.response.BaseResponse;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.*;

@Singleton
public class BizCacheRepository {

    final private DataapiHttpSdk dataapiHttpSdk;

    @Inject
    public BizCacheRepository(DataService dataService){
        dataapiHttpSdk = dataService.getDataapiHttpSdk();
    }

    public Optional<BizCache> find(String tenantId, String bizCode, String cacheType){
        String sql = "select tenantId,bizCode,cacheType,value,createTime,updateTime from %s where tenantId = :tenantId and bizCode = :bizCode and cacheType = :cacheType";
        String fqn = ModelTags.DATA_FQN_FAST_BIZCACHE;
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("bizCode", bizCode);
        params.put("cacheType", cacheType);
        BaseResponse<Map> response = dataapiHttpSdk.execute(String.format(sql, fqn), params);
        if(CollectionUtils.isNotEmpty(response.getData())){
            return Optional.of(JsonUtil.convert(response.getData().get(0), BizCache.class));
        }
        return Optional.empty();
    }

    public List<BizCache> findAll(){
        String sql = "select tenantId,bizCode,cacheType,value,createTime,updateTime from %s";
        String fqn = ModelTags.DATA_FQN_FAST_BIZCACHE;
        BaseResponse<Map> response = dataapiHttpSdk.execute(String.format(sql, fqn), new HashMap<>());
        if(CollectionUtils.isNotEmpty(response.getData())){
            List<BizCache> list = new ArrayList<>(response.getData().size());
            for (Map map : response.getData()) {
                list.add(JsonUtil.convert(map, BizCache.class));
            }
            return list;
        }
        return Collections.emptyList();
    }
}
