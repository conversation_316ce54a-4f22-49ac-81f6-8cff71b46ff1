package com.shuyun.apaas.connector.fast.model.http.result.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 角色信息类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "角色信息")
public class RoleInfo {

    @Schema(title = "角色ID", description = "角色的唯一标识")
    private String id;

    @Schema(title = "角色名称", description = "角色名称")
    private String name;

    @Schema(title = "角色类型", description = "角色类型")
    private String roleType;

    public RoleInfo() {
    }

    public RoleInfo(String id, String name, String roleType) {
        this.id = id;
        this.name = name;
        this.roleType = roleType;
    }
}
