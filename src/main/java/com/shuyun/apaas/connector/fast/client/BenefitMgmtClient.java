package com.shuyun.apaas.connector.fast.client;

import com.shuyun.ticket.benefit.domain.BenefitProduct;
import com.shuyun.ticket.benefit.domain.BenefitProject;
import com.shuyun.ticket.benefit.vo.Page;
import com.shuyun.ticket.benefit.vo.request.project.ProjectQueryVo;
import io.swagger.v3.oas.annotations.Operation;
import retrofit2.http.*;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

public interface BenefitMgmtClient {
    @Operation(summary = "卡券项目列表")
    @POST("/benefit-mgmt/v1/api/project/list?_i18n_query_=translate")
    Page<Map> projectList(@Valid @Body ProjectQueryVo request);

    @Operation(summary = "卡券项目详情")
    @GET("/benefit-mgmt/v1/api/project/{id}?_i18n_query_=translate")
    BenefitProject projectDetail(@Path("id") @NotNull String projectId);

    @Operation(summary = "卡券模板详情")
    @GET("/benefit-mgmt/v1/internal/template/{id}?_i18n_query_=translate")
    BenefitProduct templateDetail(@Path("id") @NotNull String templateId);
}
