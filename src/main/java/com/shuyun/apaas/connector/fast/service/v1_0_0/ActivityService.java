package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.cache.v1_0_0.ActivityCache;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.apaas.connector.fast.model.http.param.activity.ActivityPrizeRecordSyncParam;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.dm.dataapi.sdk.client.DataapiHttpSdk;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.constant.ModelTags;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 活动服务类
 * 参考MDM服务实现
 *
 * <AUTHOR>
 * @version 1.0
 */
@Singleton
@Slf4j
public class ActivityService {

    private final DataapiHttpSdk dataapiHttpSdk;
    private final BizCacheService bizCacheService;
    private final String tenantId;
    private final MemberService memberService;

    @Inject
    public ActivityService(BizCacheService bizCacheService,
                           FastConfig config,
                           DataService dataService, MemberService memberService) {
        this.dataapiHttpSdk = dataService.getDataapiHttpSdk();
        this.bizCacheService = bizCacheService;
        this.tenantId = config.getEnv();
        this.memberService = memberService;
    }

    /**
     * 获取活动缓存配置
     */
/*    public ActivityCache bizCacheGet(ApiBaseParam param) {
        ActivityCache cache = bizCacheService.get(ActivityCache.class, tenantId, param.getBizCode(), BizCache.ACTIVITY)
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.isNull(cache)) {
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.ACTIVITY);
        }
        return cache;
    }*/

    /**
     * 活动领奖记录同步
     */
    public Void prizeRecordSync(ActivityPrizeRecordSyncParam param) {
        String fqn = param.fqn();
        //ActivityCache cache = bizCacheGet(param);
        //param.setMemberType(param.getBizCode());

        // 设置最后同步时间
        param.setLastSync(LocalDateTime.now());

        Map<String, Object> map = new HashMap<>();
        // 过滤掉系统字段，只保留业务字段
        map.putAll(JsonUtil.filterConvert(param, Map.class, "activityPrizeRecordSyncParamFilter",
                "tenantId", "bizCode", "requestChannel", "requestSystem", "transactionId", "extension","isValid"));

        // 添加扩展字段
        if (CollectionUtils.isNotEmpty(param.getExtension())) {
            map.putAll(param.getExtension());
        }
        //map.put("memberId",memberService.getMemberId(param.getBizCode(), param.getRequestChannel(), param.getUserId()));
        log.info("activity prize record data:{}", JsonUtil.serialize(map));

        // 使用upsert方式同步数据，支持新增和更新
        dataapiHttpSdk.upsert(String.format(fqn, param.getBizCode()), param.getId(), map, false);

        return null;
    }
}
