package com.shuyun.apaas.connector.fast.client;

import com.shuyun.ticket.prepaid.card.vo.CardDetail;
import com.shuyun.ticket.prepaid.card.vo.CardVo;
import com.shuyun.ticket.prepaid.card.vo.query.CardDetailQuery;
import com.shuyun.ticket.prepaid.card.vo.query.CardQuery;
import com.shuyun.ticket.vo.query.PageBean;
import retrofit2.http.*;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 后续用到再引入其他接口
 */
public interface PrepaidCardClient {

    @POST("/prepaid-card-service/v1/api/cards/pageQuery")
    PageBean<CardVo> cardQuery(@Valid @NotNull @Body CardQuery query);

    @POST("/prepaid-card-service/v1/api/cards/detailQuery")
    CardDetail detailQuery(@Valid @NotNull @Body CardDetailQuery query);
}
