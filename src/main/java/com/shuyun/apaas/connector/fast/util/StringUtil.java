package com.shuyun.apaas.connector.fast.util;

import io.micronaut.core.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class StringUtil {

    public static List<String> toStringList(List<Object> values){
        if(CollectionUtils.isEmpty(values)){
            return null;
        }
        List<String> stringList = new ArrayList<>();
        values.forEach(v->{
            stringList.add(String.valueOf(v));
        });
        return stringList;
    }
}
