package com.shuyun.apaas.connector.fast.handler.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.MbspCache;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.util.DateUtil;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.MemberDeregisterParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.request.MemberCancellationRequest;
import com.shuyun.kylin.member.api.response.MemberBaseResponse;
import com.shuyun.kylin.member.api.response.MemberBaseResult;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberDeregisterApiHandler extends AbstractApiHandler<MemberDeregisterParam, MemberId, MemberCancellationRequest, MemberBaseResult> {

    private final MemberService memberService;

    @Inject
    public MemberDeregisterApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberDeregisterParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.singleEmptyValidate(userId,  "userId");
        FastValidator.singleRequiredValidate(memberId,  "memberId");
    }

    @Override
    public MemberDeregisterParam beforeRequest(MemberDeregisterParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public MemberCancellationRequest prepareParam(MemberDeregisterParam param) {
        MbspCache cache = memberService.bizCacheGet(param);
        MemberCancellationRequest request = new MemberCancellationRequest();
        request.setProgramCode(cache.getProgramId());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setShopCode(param.getShopCode());
        request.setChannelType(param.getRequestChannel());
        // TODO: 2024/3/14 时间转换
        request.setCancelTime(DateUtil.utcTime(param.getDeregisterTime()));
        request.setRemark(param.getRemark());
        request.setOperator(param.getOperator());
        return request;
    }

    @Override
    public MemberBaseResult request(MemberCancellationRequest invokeParam) {
        return memberService.deregister(invokeParam);
    }

    @Override
    public MemberId prepareResult(MemberDeregisterParam param, MemberBaseResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            MemberBaseResponse response = result.getData();
            MemberId memberId = new MemberId();
            memberId.setMemberId(response.getMemberId());
            return memberId;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500204, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_DEREGISTER;
    }
}
