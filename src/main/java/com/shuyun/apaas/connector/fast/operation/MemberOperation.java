package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.domain.DynamicCode;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.*;
import com.shuyun.fast.v1_0_0.result.MemberBindResult;
import com.shuyun.fast.v1_0_0.result.MemberGetResult;
import com.shuyun.fast.v1_0_0.result.MemberRegisterResult;
import jakarta.inject.Inject;

@Title("会员场景")
public class MemberOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("会员查询")
    @Api(name = ApiTags.API_NAME_MEMBER_GET)
    public ApiResult<MemberGetResult> get(@Parameter MemberGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员注册")
    @Api(name = ApiTags.API_NAME_MEMBER_REGISTER)
    public ApiResult<MemberRegisterResult> register(@Parameter MemberRegisterParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员绑定")
    @Api(name = ApiTags.API_NAME_MEMBER_REGISTER)
    public ApiResult<MemberBindResult> bind(@Parameter MemberBindParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员解绑")
    @Api(name = ApiTags.API_NAME_MEMBER_UNBIND)
    public ApiResult<MemberId> unbind(@Parameter MemberUnbindParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员修改")
    @Api(name = ApiTags.API_NAME_MEMBER_MODIFY)
    public ApiResult<MemberId> modify(@Parameter MemberModifyParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员手机号修改")
    @Api(name = ApiTags.API_NAME_MEMBER_MOBILE_MODIFY)
    public ApiResult<MemberId> mobileModify(@Parameter MemberMobileModifyParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员注销")
    @Api(name = ApiTags.API_NAME_MEMBER_DEREGISTER)
    public ApiResult<MemberId> deregister(@Parameter MemberDeregisterParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员动态码获取")
    @Api(name = ApiTags.API_NAME_MEMBER_DYNAMICCODE_GET)
    public ApiResult<DynamicCode> dynamicCodeGet(@Parameter MemberDynamicCodeGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("会员动态码识别")
    @Api(name = ApiTags.API_NAME_MEMBER_DYNAMICCODE_IDENTIFY)
    public ApiResult<MemberId> dynamicCodeIdentify(@Parameter MemberDynamicCodeIdentifyParam param)  {
        return handlerRouter.route(param).handle(param);
    }

}
