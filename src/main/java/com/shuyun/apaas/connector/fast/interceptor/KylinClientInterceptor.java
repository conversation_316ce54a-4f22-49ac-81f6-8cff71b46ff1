package com.shuyun.apaas.connector.fast.interceptor;

import com.google.gson.Gson;
import com.shuyun.air.kylin.micronaut.support.discovery.RoundRobinLoadBalancer;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.exception.model.KylinSdkErrorResult;
import com.shuyun.fast.base.ApiTags;
import io.micronaut.discovery.ServiceInstance;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import java.io.IOException;

@Slf4j
public class KylinClientInterceptor implements Interceptor {

    private final Boolean clientDiscoveryEnable;
    private final RoundRobinLoadBalancer loadBalancer;
    private final String publicAddress;
    public KylinClientInterceptor(Boolean clientDiscoveryEnable,
                                  String publicAddress,
                                  RoundRobinLoadBalancer loadBalancer){
        this.clientDiscoveryEnable = clientDiscoveryEnable;
        this.publicAddress = publicAddress;
        this.loadBalancer = loadBalancer;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request originalRequest = chain.request();
        String url = originalRequest.url().toString();
        if(clientDiscoveryEnable && url.contains(publicAddress)){
            log.info("kylin original client url:{}", url);
            ServiceInstance instance = loadBalancer.selectInMemory(null);
            String targetInstanceAddress = String.join(":", instance.getHost(), String.valueOf(instance.getPort()));
            String privateUrl = url.replace(publicAddress, targetInstanceAddress);
            Request privateRequest = originalRequest.newBuilder()
                    .url(privateUrl.replace("https://",  "http://"))
                    .build();
            log.info("kylin private client url:{}", privateUrl);
            return this.proceed(chain, privateRequest);
        }else{
            return this.proceed(chain, originalRequest);
        }
    }

    private Response proceed(@NotNull Chain chain, Request request) throws IOException {
        Response response =  chain.proceed(request);
        if(response.code() >= 400){
            String errBody = response.body().string();
            Gson gson = new Gson();
            KylinSdkErrorResult errorResult = gson.fromJson(errBody, KylinSdkErrorResult.class);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, errorResult.getMsg());
        }
        return response;
    }
}
