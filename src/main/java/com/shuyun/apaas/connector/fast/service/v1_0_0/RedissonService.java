package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.config.RedissonConfig;
import com.shuyun.lite.util.Common;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.Getter;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;

import java.util.Arrays;
import java.util.stream.Collectors;

@Singleton
public class RedissonService {

    @Getter
    private RedissonClient redissonClient;
    private RedissonConfig redissonConfig;

    @Inject
    public RedissonService(FastConfig config){
        redissonConfig = config.getRedis();
        redissonClient = get();
    }

    private RedissonClient get() {
        boolean isSsl = Boolean.parseBoolean(redissonConfig.getSsl());
        String redisAddress = redissonConfig.getAddress();
        if(StringUtils.isEmpty(redisAddress)){
            redisAddress = Common.getSysOrEnv("redis.address");
        }
        String address = (String) Arrays.stream(redisAddress.split(",")).map((add) -> {
            if (!add.startsWith("redis://")) {
                add = "redis://" + add;
            }

            if (isSsl) {
                add = add.replace("redis:", "rediss:");
            }

            return add;
        }).collect(Collectors.joining(","));
        String redisPassword = redissonConfig.getPassword();
        if(StringUtils.isEmpty(redisPassword)){
            redisPassword = Common.getSysOrEnv("redis.password");
        }
        return !StringUtils.isEmpty(address) && address.indexOf(",") > 0 ? this.getMultiRedis(address.split(","), redisPassword, jsonJacksonCodec()) : this.getSingleRedis(address, redisPassword, jsonJacksonCodec());
    }

    public JsonJacksonCodec jsonJacksonCodec() {
        return new JsonJacksonCodec();
    }

    private RedissonClient getSingleRedis(String address, String password, JsonJacksonCodec jsonJacksonCodec) {
        Config config = new Config();
        config.setCodec(jsonJacksonCodec);
        SingleServerConfig singleServerConfig = config.useSingleServer();
        int poolSize = Integer.parseInt(this.redissonConfig.getPoolSize());
        if (poolSize < 64) {
            poolSize = 64;
        }

        singleServerConfig.setAddress(address).setDatabase(Integer.parseInt(this.redissonConfig.getDatabase())).setConnectionPoolSize(poolSize);
        if (!StringUtils.isEmpty(password)) {
            singleServerConfig.setPassword(password);
        }

        return Redisson.create(config);
    }

    private RedissonClient getMultiRedis(String[] addresses, String password, JsonJacksonCodec jsonJacksonCodec) {
        Config config = new Config();
        config.setNettyThreads(64);
        config.setCodec(jsonJacksonCodec);
        ClusterServersConfig clusterServersConfig = config.useClusterServers();
        clusterServersConfig.setScanInterval(2000).addNodeAddress(addresses).setMasterConnectionPoolSize(Integer.parseInt(this.redissonConfig.getPoolSize()));
        if (!StringUtils.isEmpty(password)) {
            clusterServersConfig.setPassword(password);
        }

        return Redisson.create(config);
    }
}
