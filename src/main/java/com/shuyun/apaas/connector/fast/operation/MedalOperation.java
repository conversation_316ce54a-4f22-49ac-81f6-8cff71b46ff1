package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.domain.Medal;
import com.shuyun.fast.v1_0_0.param.medal.MedalGetParam;
import jakarta.inject.Inject;

import java.util.List;

@Title("勋章场景")
public class MedalOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("会员勋章查询")
    @Api(name = ApiTags.API_NAME_MEDAL_GET)
    public ApiResult<List<Medal>> get(@Parameter MedalGetParam param){
        return handlerRouter.route(param).handle(param);
    }

}
