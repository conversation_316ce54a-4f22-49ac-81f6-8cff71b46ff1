package com.shuyun.apaas.connector.fast.util;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;

/**
 * 日期计算示例
 * 演示"次年同月最后1天"的计算逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class DateCalculationExample {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSSSSS");

    public static void main(String[] args) {
        System.out.println("=== 积分过期日期计算示例 ===\n");

        // 测试各种情况
        testCalculation("2024年3月15日", LocalDateTime.of(2024, Month.MARCH, 15, 10, 30, 0));
        testCalculation("2024年2月15日（闰年）", LocalDateTime.of(2024, Month.FEBRUARY, 15, 14, 20, 0));
        testCalculation("2025年2月15日（平年）", LocalDateTime.of(2025, Month.FEBRUARY, 15, 14, 20, 0));
        testCalculation("2024年4月15日（30天月）", LocalDateTime.of(2024, Month.APRIL, 15, 12, 0, 0));
        testCalculation("2024年12月10日", LocalDateTime.of(2024, Month.DECEMBER, 10, 16, 0, 0));
        testCalculation("2024年1月31日（月末）", LocalDateTime.of(2024, Month.JANUARY, 31, 23, 59, 59));

        System.out.println("=== 计算完成 ===");
    }

    private static void testCalculation(String description, LocalDateTime currentTime) {
        LocalDateTime overdueDate = calculateOverdueDate(currentTime, "POINT");
        
        System.out.println("测试场景: " + description);
        System.out.println("当前时间: " + currentTime.format(FORMATTER));
        System.out.println("过期时间: " + overdueDate.format(FORMATTER));
        System.out.println("说明: " + getExplanation(currentTime, overdueDate));
        System.out.println();
    }

    /**
     * 计算过期日期
     * Overdue = 次年同月最后1天
     */
    private static LocalDateTime calculateOverdueDate(LocalDateTime currentTime, String pointBizType) {
        if ("POINT".equals(pointBizType)) {
            // 次年同月最后1天
            LocalDateTime nextYear = currentTime.plusYears(1);
            
            // 获取次年同月的最后一天
            LocalDateTime lastDayOfMonth = nextYear.withDayOfMonth(1)  // 先设置为当月第一天
                    .plusMonths(1)  // 加一个月
                    .minusDays(1)   // 减一天，得到上个月的最后一天
                    .withHour(23)   // 设置为当天的最后时刻
                    .withMinute(59)
                    .withSecond(59)
                    .withNano(999999999);
            
            return lastDayOfMonth;
        } else {
            // 其他类型默认加一年
            return currentTime.plusYears(1);
        }
    }

    private static String getExplanation(LocalDateTime current, LocalDateTime overdue) {
        int currentYear = current.getYear();
        int overdueYear = overdue.getYear();
        Month currentMonth = current.getMonth();
        Month overdueMonth = overdue.getMonth();
        int overdueDayOfMonth = overdue.getDayOfMonth();
        
        String monthName = getMonthName(currentMonth);
        String explanation = String.format("从%d年%s到%d年%s的最后一天（%d日）", 
                currentYear, monthName, overdueYear, monthName, overdueDayOfMonth);
        
        if (currentMonth == Month.FEBRUARY) {
            boolean isLeapYear = overdueYear % 4 == 0 && (overdueYear % 100 != 0 || overdueYear % 400 == 0);
            explanation += String.format("，%d年是%s", overdueYear, isLeapYear ? "闰年" : "平年");
        }
        
        return explanation;
    }

    private static String getMonthName(Month month) {
        switch (month) {
            case JANUARY: return "1月";
            case FEBRUARY: return "2月";
            case MARCH: return "3月";
            case APRIL: return "4月";
            case MAY: return "5月";
            case JUNE: return "6月";
            case JULY: return "7月";
            case AUGUST: return "8月";
            case SEPTEMBER: return "9月";
            case OCTOBER: return "10月";
            case NOVEMBER: return "11月";
            case DECEMBER: return "12月";
            default: return month.toString();
        }
    }
}
