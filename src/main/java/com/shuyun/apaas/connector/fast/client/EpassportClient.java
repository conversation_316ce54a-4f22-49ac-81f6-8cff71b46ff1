package com.shuyun.apaas.connector.fast.client;

import com.shuyun.epassport.sdk.*;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

public interface EpassportClient {
    @Operation(summary = "组织创建")
    @POST("/epassport/v1/admin/orgs")
    Org create(@Valid @Body Org org);

    @Operation(summary = "组织更新")
    @PUT("/epassport/v1/admin/orgs/{id}")
    Void update(@Path("id") @NotNull Long id,
                      @Valid @Body Org org);
    @Operation(summary = "组织删除")
    @DELETE("/epassport/v1/admin/orgs/{id}")
    Void delete(@Path("id") @NotNull Long id);

    @Operation(summary = "分页查询组织信息")
    @GET("/epassport/v1/admin/orgs/page")
    Page<Org> list(@Query(value = "code") String code,
                   @Query(value = "name") String name);

    @Operation(summary = "token查用户信息")
    @GET("/epassport/v1/account/userInfo")
    Map<String,Object> userInfo(@Header(value = "Authorization") String Authorization);
}
