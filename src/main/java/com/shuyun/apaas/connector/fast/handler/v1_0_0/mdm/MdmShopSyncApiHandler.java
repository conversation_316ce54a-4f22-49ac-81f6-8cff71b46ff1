package com.shuyun.apaas.connector.fast.handler.v1_0_0.mdm;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MdmService;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopSyncParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class MdmShopSyncApiHandler extends AbstractApiHandler<MdmShopSyncParam, Void, MdmShopSyncParam, Void> {

    private final MdmService mdmService;

    @Inject
    public MdmShopSyncApiHandler(MdmService mdmService){
        this.mdmService = mdmService;
    }

    @Override
    public void validate(MdmShopSyncParam param) {
        super.validate(param);
    }


    @Override
    public MdmShopSyncParam beforeRequest(MdmShopSyncParam param) {
        super.beforeRequest(param);
        param.setId(param.getShopCode());
        return param;
    }

    @Override
    public MdmShopSyncParam prepareParam(MdmShopSyncParam param) {
        return param;
    }

    @Override
    public Void request(MdmShopSyncParam invokeParam) {
        return mdmService.shopSync(invokeParam);
    }

    @Override
    public Void prepareResult(MdmShopSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_SHOP_SYNC;
    }
}
