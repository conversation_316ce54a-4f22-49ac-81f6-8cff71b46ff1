package com.shuyun.apaas.connector.fast.service.v1_0_0;


import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.model.http.param.auth.SsoLoginParam;
import com.shuyun.apaas.connector.fast.model.http.result.auth.RoleInfo;
import com.shuyun.apaas.connector.fast.model.http.result.auth.SsoLoginResult;
import java.util.Map;
import com.shuyun.fast.base.ApiTags;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 认证服务类
 * 参考其他服务实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Singleton
@Slf4j
public class AuthService {

    private final EpassportService epassportService;

    @Inject
    public AuthService(EpassportService epassportService) {
        this.epassportService = epassportService;
    }

    /**
     * 单点登录
     * 
     * @param param 登录参数
     * @return 登录结果
     */
    public SsoLoginResult ssoLogin(SsoLoginParam param) {
        try {
            log.info("开始处理单点登录，token长度：{}", param.getToken().length());

            // 验证并格式化token
            String authHeader = epassportService.validateAndFormatToken(param.getToken());

            // 调用EpassportService获取用户信息
            Map<String, Object> userInfo = epassportService.getUserInfo(authHeader);

            if (userInfo == null) {
                log.warn("获取用户信息失败，token无效");
                throw new ApiException(ApiTags.API_RESP_CODE_500100, "无效的访问令牌");
            }

            // 构建登录结果
            SsoLoginResult result = buildSsoLoginResult(userInfo);

            log.info("单点登录成功，用户ID：{}，用户名：{}", result.getId(), result.getUsername());
            return result;

        } catch (ApiException e) {
            log.error("单点登录业务异常：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("单点登录系统异常", e);
            throw new ApiException(ApiTags.API_RESP_CODE_500100, "系统异常，请稍后重试");
        }
    }

    /**
     * 构建单点登录结果
     */
    private SsoLoginResult buildSsoLoginResult(Map<String, Object> userInfo) {
        SsoLoginResult result = new SsoLoginResult();

        // 设置基本用户信息
        result.setId(getUserId(userInfo));
        result.setType(getUserType(userInfo));
        result.setRealname(getRealname(userInfo));
        result.setUsername(getUsername(userInfo));
        result.setEmployeeNo(getEmployeeNo(userInfo));
        result.setEnabled(getEnabled(userInfo));
        result.setLocked(getLocked(userInfo));
        result.setMobile(getMobile(userInfo));

        // 设置角色信息
        result.setRoles(buildRoleInfoList(userInfo));

        return result;
    }

    /**
     * 获取用户ID
     */
    private String getUserId(Map<String, Object> userInfo) {
        try {
            // 从Map中获取用户ID
            return getMapValue(userInfo, "id", "userId", "ID");
        } catch (Exception e) {
            log.warn("获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 获取用户类型
     */
    private String getUserType(Map<String, Object> userInfo) {
        try {
            String type = getMapValue(userInfo, "type", "userType");
            return type != null ? type : "USER";
        } catch (Exception e) {
            log.warn("获取用户类型失败", e);
            return "USER"; // 默认用户类型
        }
    }

    /**
     * 获取真实姓名
     */
    private String getRealname(Map<String, Object> userInfo) {
        return getMapValue(userInfo, "realname", "realName", "fullName", "name");
    }

    /**
     * 获取用户名
     */
    private String getUsername(Map<String, Object> userInfo) {
        return getMapValue(userInfo, "username", "userName", "loginName", "account");
    }

    /**
     * 获取员工编号
     */
    private String getEmployeeNo(Map<String, Object> userInfo) {
        return getMapValue(userInfo, "employeeNo", "employeeNumber", "empNo", "workNo");
    }

    /**
     * 获取是否启用
     */
    private Boolean getEnabled(Map<String, Object> userInfo) {
        Boolean enabled = getBooleanMapValue(userInfo, "enabled", "isEnabled", "active", "isActive");
        return enabled != null ? enabled : true; // 默认启用
    }

    /**
     * 获取是否锁定
     */
    private Boolean getLocked(Map<String, Object> userInfo) {
        Boolean locked = getBooleanMapValue(userInfo, "locked", "isLocked", "disabled", "isDisabled");
        return locked != null ? locked : false; // 默认未锁定
    }

    /**
     * 获取手机号
     */
    private String getMobile(Map<String, Object> userInfo) {
        return getMapValue(userInfo, "mobile", "phone", "phoneNumber", "cellphone");
    }

    /**
     * 构建角色信息列表
     */
    private List<RoleInfo> buildRoleInfoList(Map<String, Object> userInfo) {
        List<RoleInfo> roles = new ArrayList<>();

        try {
            // 从Map中获取角色列表
            Object userRoles = getMapValueAsObject(userInfo, "roles", "roleList", "authorities");

            if (userRoles instanceof List) {
                List<?> roleList = (List<?>) userRoles;
                for (Object role : roleList) {
                    if (role != null) {
                        RoleInfo roleInfo = new RoleInfo();
                        if (role instanceof Map) {
                            Map<String, Object> roleMap = (Map<String, Object>) role;
                            roleInfo.setId(getMapValue(roleMap, "id", "roleId", "ID"));
                            roleInfo.setName(getMapValue(roleMap, "name", "roleName", "title"));
                            roleInfo.setRoleType(getMapValue(roleMap, "roleType", "type", "category"));
                        } else {
                            // 如果不是Map，使用反射
                            roleInfo.setId(getFieldValue(role, "id", "roleId", "ID"));
                            roleInfo.setName(getFieldValue(role, "name", "roleName", "title"));
                            roleInfo.setRoleType(getFieldValue(role, "roleType", "type", "category"));
                        }
                        roles.add(roleInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取角色信息失败", e);
            // 返回空列表
        }

        return roles;
    }

    /**
     * 从Map中获取字符串值
     */
    private String getMapValue(Map<String, Object> map, String... fieldNames) {
        if (map == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            Object value = map.get(fieldName);
            if (value != null) {
                return String.valueOf(value);
            }
        }

        return null;
    }

    /**
     * 从Map中获取布尔值
     */
    private Boolean getBooleanMapValue(Map<String, Object> map, String... fieldNames) {
        String value = getMapValue(map, fieldNames);
        if (value == null) {
            return null;
        }

        try {
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从Map中获取对象值
     */
    private Object getMapValueAsObject(Map<String, Object> map, String... fieldNames) {
        if (map == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            Object value = map.get(fieldName);
            if (value != null) {
                return value;
            }
        }

        return null;
    }

    /**
     * 获取对象类型的字段值（保留用于角色处理）
     */
    private Object getFieldValueAsObject(Object obj, String... fieldNames) {
        if (obj == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            try {
                // 尝试通过getter方法获取
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                Method method = obj.getClass().getMethod(getterName);
                return method.invoke(obj);
            } catch (Exception e) {
                // 忽略异常，尝试下一个字段名
            }

            try {
                // 尝试直接访问字段
                Field field = obj.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(obj);
            } catch (Exception e) {
                // 忽略异常，尝试下一个字段名
            }
        }

        return null;
    }

    /**
     * 通用字段值获取方法
     * 尝试多个可能的字段名获取值
     */
    private String getFieldValue(Object obj, String... fieldNames) {
        if (obj == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            try {
                // 尝试通过getter方法获取
                String getterName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                Method method = obj.getClass().getMethod(getterName);
                Object value = method.invoke(obj);
                if (value != null) {
                    return String.valueOf(value);
                }
            } catch (Exception e) {
                // 忽略异常，尝试下一个字段名
            }

            try {
                // 尝试直接访问字段
                Field field = obj.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null) {
                    return String.valueOf(value);
                }
            } catch (Exception e) {
                // 忽略异常，尝试下一个字段名
            }
        }

        return null;
    }

    /**
     * 获取布尔类型字段值
     */
    private Boolean getBooleanFieldValue(Object obj, String... fieldNames) {
        String value = getFieldValue(obj, fieldNames);
        if (value == null) {
            return null;
        }

        try {
            return Boolean.parseBoolean(value);
        } catch (Exception e) {
            return null;
        }
    }
}
