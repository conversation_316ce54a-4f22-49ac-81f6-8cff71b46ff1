package com.shuyun.apaas.connector.fast.client;

import com.shuyun.cdp.common.response.BaseResponse;
import com.shuyun.cdp.tags.request.openapi.CustomerTagQueryRequest;
import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.cdp.tags.request.openapi.CustomersTagOperateRequest;
import com.shuyun.cdp.tags.response.openapi.CategoryTreeResponse;
import com.shuyun.cdp.tags.response.openapi.CustomerTagQueryResponse;
import com.shuyun.cdp.tags.response.openapi.TagListResponse;
import retrofit2.http.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import retrofit2.http.PUT;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Set;

public interface CdpClient {
    @Operation(summary = "标签目录列表")
    @GET("/cdp-mgmt/v1/openapi/{domainCode}/category")
    CategoryTreeResponse getCategory(@NotNull @Path("domainCode") String domainCode,
                                     @Query(value = "categoryId") Long categoryId);

    @Operation(summary = "标签列表")
    @GET("/cdp-mgmt/v1/openapi/{domainCode}/tag")
    TagListResponse getTags(@NotNull @Path("domainCode") String domainCode,
                            @Query(value = "categoryIds") @NotNull Set<Long> categoryIds,
                            @Parameter(description = "当前页,从0开始", example = "0") @Query(value = "pageNo") Integer pageNo,
                            @Parameter(description = "每页记录数 ", example = "20") @Query(value = "pageSize") Integer pageSize);


    @Operation(summary = "多个消费者打标去标同一个标签")
    @PUT("/cdp-mgmt/v1/openapi/{domainCode}/customersTag/operate")
    BaseResponse<String> operateCustomersTag(@NotNull @Path("domainCode") String domainCode,
                                             @Valid @Body CustomersTagOperateRequest customersTagOperateRequest);

    @Operation(summary = "单个消费者打标去标多个标签")
    @PUT("/cdp-mgmt/v1/openapi/{domainCode}/customerTags/operate")
    BaseResponse<String> operateCustomerTags(@NotNull @Path("domainCode") String domainCode,
                                             @Valid @Body CustomerTagsOperateRequest customerTagsOperateRequest);


    @Operation(summary = "消费者标签查询")
    @POST("/cdp-mgmt/v1/openapi/{domainCode}/customerTags/query")
    CustomerTagQueryResponse getTagsByCustomer(@NotNull @Path("domainCode") String domainCode,
                                               @Valid @Body CustomerTagQueryRequest customerTagQueryRequest);

}
