package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.client.EbrandMemberClient;
import com.shuyun.apaas.connector.fast.client.MbspClient;
import com.shuyun.apaas.connector.fast.client.manager.ClientManager;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.apaas.connector.fast.tuple.Tuple2;
import com.shuyun.apaas.connector.fast.util.SnowFlake;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.MbspCache;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.util.DateUtil;
import com.shuyun.apaas.connector.fast.util.JsonUtil;
import com.shuyun.fast.v1_0_0.domain.DynamicCode;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.*;
import com.shuyun.fast.v1_0_0.result.MobileEncryptResult;
import com.shuyun.kylin.member.api.enums.ChannelMemberStatusEnum;
import com.shuyun.kylin.member.api.request.*;
import com.shuyun.kylin.member.api.response.*;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Singleton
@Slf4j
public class MemberService {
    private final MbspClient mbspClient;
    private final BizCacheService bizCacheService;
    private final EbrandMemberClient ebrandClient;
    private final SnowFlake snowFlake;
    private final RedissonClient redissonClient;
    private final String tenantId;
    private final FastConfig config;
    @Inject
    public MemberService(ClientManager clientManager,
                         BizCacheService bizCacheService,
                         SnowFlake snowFlake,
                         RedissonService redissonService,
                         FastConfig config){
        tenantId = config.getEnv();
        this.config = config;
        this.mbspClient = clientManager.getMbspClient();
        this.bizCacheService = bizCacheService;
        this.ebrandClient = clientManager.getEbrandMemberClient();
        this.snowFlake = snowFlake;
        this.redissonClient = redissonService.getRedissonClient();
    }

    public MbspCache bizCacheGet(ApiBaseParam param){
        MbspCache cache = bizCacheService.get(MbspCache.class, tenantId, param.getBizCode(), BizCache.MBSP)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.MBSP);
        }
        return cache;
    }

    public Boolean memberExists(ApiBaseParam param, String memberId){
        MbspCache cache = bizCacheGet(param);
        MemberQueryRequest request = new MemberQueryRequest();
        request.setProgramCode(cache.getProgramId());
        request.setEnableCancellation(false);
        request.setMemberId(memberId);
        MemberQueryResult result = mbspClient.query(request);
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            return StringUtils.isNotEmpty(result.getData().getMemberId());
        }
        return false;
    }

    /**
     * 查询会员id,mobile最高优先级
     * 本方法考虑性能可以使用实例级缓存，但需要考虑手机号、渠道id解绑场景的对应的缓存失效场景
     * @param bizCode 业务编码
     * @param channel
     * @param userId
     * @return
     */
    public String getMemberId(String bizCode, String channel, String userId){
        log.info("get memberId from mbsp module by userId:{}", userId);
        ApiBaseParam param = new MemberGetParam();
        param.setTenantId(tenantId);
        param.setBizCode(bizCode);
        MbspCache cache = bizCacheGet(param);
        MemberQueryByCustomerNoRequest request = new MemberQueryByCustomerNoRequest();
        request.setProgramCode(cache.getProgramId());
        request.setEnrollChannel(channel);
        request.setCustomerNo(userId);
        MemberQueryResult result = mbspClient.queryMemberInfoByChannelId(request);
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            return result.getData().getMemberId();
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500203, result.getMsg());
    }

    public MemberIdentifyParam memberIdentify(MemberIdentifyParam identify,  ApiBaseParam param) throws ApiException{
        if(StringUtils.isNotEmpty(identify.getMemberId())){
            return identify;
        }
        String userId = identify.getUserId();
        String channel = identify.getChannel();
        if(StringUtils.isEmpty(channel)){
            identify.setChannel(param.getRequestChannel());
        }
        String memberId = getMemberId(param.getBizCode(), identify.getChannel(), userId);
        if(StringUtils.isNotEmpty(memberId)){
            identify.setMemberId(memberId);
            return identify;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500000, userId);
    }

    public List<String> mobileEncrypt(String mobile, String programId){
        MobileEncryptParam encryptParam = new MobileEncryptParam();
        encryptParam.setMobile(mobile);
        encryptParam.setModelUnit(programId);
        MobileEncryptResult encryptResult = ebrandClient.encrypt(encryptParam);
        if(!CollectionUtils.isEmpty(encryptResult.getMixMobiles())){
            //密文手机号添加
            return encryptResult.getMixMobiles()
                    .stream()
                    .map(m->m.getMixMobile())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    public Tuple2<MemberQueryResult, MemberBindingQueryResult> get(MemberGetParam param) {
        MbspCache cache = bizCacheGet(param);
        MemberIdentifyParam identify = param.getIdentify();
        MemberQueryResult memberQueryResult = null;
        MemberBindingQueryResult bindingQueryResult = null;
        if(StringUtils.isNotEmpty(param.getMobile())){//根据手机号查询,如果有会员通需要先请求会员通服务拿密文(淘宝密文会员匹配)
            MemberQueryByIdentifyRequest request = new MemberQueryByIdentifyRequest();
            request.setProgramCode(cache.getProgramId());
            List<String> mobileValues = new ArrayList<>();
            mobileValues.add(param.getMobile());
            if(config.getEbrandEnable()){
                mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
            }
            MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
            identifyInfo.setCode("mobile");
            identifyInfo.setValues(mobileValues);
            request.setIdentifies(Collections.singletonList(identifyInfo));
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.queryByIdentify(request);
        } else if(StringUtils.isNotEmpty(identify.getMemberId())){//根据memberId查询
            MemberQueryRequest request = new MemberQueryRequest();
            request.setProgramCode(cache.getProgramId());
            request.setEnableCancellation(false);
            request.setMemberId(param.getIdentify().getMemberId());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.query(request);
        } else{//根据userId查询
            MemberQueryByCustomerNoRequest request = new MemberQueryByCustomerNoRequest();
            request.setProgramCode(cache.getProgramId());
            request.setEnrollChannel(param.getRequestChannel());
            request.setCustomerNo(identify.getUserId());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.queryMemberInfoByChannelId(request);
        }
        if(param.getChannelDataRequired() && Objects.nonNull(memberQueryResult.getData())){//查询渠道数据
            MemberChannelQueryRequest request = new MemberChannelQueryRequest();
            request.setEnableCancellation(false);
            if(Objects.nonNull(identify) && StringUtils.isNotEmpty(identify.getUserId())){
                request.setCustomerNo(identify.getUserId());
            }
            request.setMemberId(memberQueryResult.getData().getMemberId());
            request.setProgramCode(cache.getProgramId());
            request.setEnrollChannel(param.getRequestChannel());
            request.setChannelStatus(ChannelMemberStatusEnum.ACTIVE.name());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                request.setOptionalFields(param.getOptionalFields());
            }
            bindingQueryResult = mbspClient.queryChannelMembers(request);
        }
        return new Tuple2<>(memberQueryResult, bindingQueryResult);
    }

    public Tuple2<MemberQueryResult, MemberBindingQueryResult> getByUnionId(MemberGetParam param){
        MbspCache cache = bizCacheGet(param);
        MemberChannelQueryByUnionIdRequest request = new MemberChannelQueryByUnionIdRequest();
        request.setProgramCode(cache.getProgramId());
        request.setEnrollChannel(param.getRequestChannel());
        request.setUnionId(param.getUnionId());
        request.setChannelStatus(ChannelMemberStatusEnum.ACTIVE.name());
        if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
            request.setOptionalFields(param.getOptionalFields());
        }
        MemberBindingQueryResult bindingQueryResult = mbspClient.queryByUnionId(request);
        MemberQueryResult memberQueryResult = null;
        if(CollectionUtils.isNotEmpty(bindingQueryResult.getData())){
            MemberQueryRequest requests = new MemberQueryRequest();
            requests.setProgramCode(cache.getProgramId());
            requests.setEnableCancellation(false);
            requests.setMemberId(bindingQueryResult.getData().get(0).getMemberId());
            if(CollectionUtils.isNotEmpty(param.getOptionalFields())){
                requests.setOptionalFields(param.getOptionalFields());
            }
            memberQueryResult = mbspClient.query(requests);
        }
        return new Tuple2<>(memberQueryResult, bindingQueryResult);
    }

    public BindResult bind(MemberBindParam param) {
        MbspCache cache = bizCacheGet(param);
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(config.getEbrandEnable()){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        BindRequest request = JsonUtil.convert(param, BindRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setBrand(cache.getBrand());
        request.setCustomerNo(param.getIdentify().getUserId());
        request.setMemberId(param.getIdentify().getMemberId());
        MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
        identifyInfo.setCode("mobile");
        identifyInfo.setValues(mobileValues);
        request.setIdentifies(Collections.singletonList(identifyInfo));
        request.setEnrollChannel(param.getRegisterChannel());
        request.setEnrollShopCode(param.getRegisterShopCode());
        request.setEnrollShopName(param.getRegisterShopName());
        request.setEnrollGuide(param.getRegisterGuide());
        // TODO: 2024/3/14 特殊字段映射
        return mbspClient.bind(request);
    }

    public RegisterResult register(MemberRegisterParam param) {
        MbspCache cache = bizCacheGet(param);
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(config.getEbrandEnable()){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        RegisterRequest request = JsonUtil.convert(param, RegisterRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setBrand(cache.getBrand());
        request.setCustomerNo(param.getIdentify().getUserId());
        MemberIdentifyInfo identifyInfo = new MemberIdentifyInfo();
        identifyInfo.setCode("mobile");
        identifyInfo.setValues(mobileValues);
        request.setIdentifies(Collections.singletonList(identifyInfo));
        request.setEnrollTime(DateUtil.utcTime(param.getRegisterTime()));
        request.setEnrollChannel(param.getRegisterChannel());
        request.setEnrollShopCode(param.getRegisterShopCode());
        request.setEnrollShopName(param.getRegisterShopName());
        request.setEnrollGuide(param.getRegisterGuide());
        // TODO: 2024/3/14  特殊字段映射
        return mbspClient.register(request);
    }

    public MemberBaseResult unbind(ChannelMemberUnbindRequest param) {
        return mbspClient.unbindChannelMember(param);
    }

    public MemberBaseResult deregister(MemberCancellationRequest param) {
        return mbspClient.memberCancellation(param);
    }

    public MemberUpdateResult modify(ChannelMemberEditRequest param) {
        return mbspClient.updateChannelMember(param);
    }

    public MemberBaseResult mobileModify(MemberMobileModifyParam param) {
        MbspCache cache = bizCacheGet(param);
        MemberMobileModifyRequest request = JsonUtil.convert(param, MemberMobileModifyRequest.class);
        request.setProgramCode(cache.getProgramId());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setNewMobile(param.getMobile());
        request.setChannelType(param.getRequestChannel());
        List<String> mobileValues = new ArrayList<>();
        mobileValues.add(param.getMobile());
        if(config.getEbrandEnable()){
            mobileValues.addAll(mobileEncrypt(param.getMobile(), cache.getProgramId()));
        }
        request.setNewMixMobiles(mobileValues);
        return mbspClient.modifyMobile(request);
    }

    public DynamicCode dynamicCodeGet(MemberDynamicCodeGetParam param) {
        String memberId = param.getIdentify().getMemberId();
        String code = String.valueOf(snowFlake.nextId());
        RBucket<String> bucket = redissonClient.getBucket(code);
        bucket.set(memberId, param.getValidSeconds(), TimeUnit.SECONDS);//设置有效期
        return new DynamicCode(code);
    }

    public MemberId dynamicCodeIdentify(MemberDynamicCodeIdentifyParam param) {
        RBucket<String> bucket = redissonClient.getBucket(param.getDynamicCode());
        MemberId m = new MemberId();
        m.setMemberId(bucket.get());
        return m;
    }

}
