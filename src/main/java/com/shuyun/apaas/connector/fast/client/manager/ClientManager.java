package com.shuyun.apaas.connector.fast.client.manager;

import com.google.common.collect.Lists;
import com.lcap.qing.retrofit.RetrofitProxy;
import com.lcap.qing.retrofit.adapter.LcapCallAdapterFactory;
import com.lcap.qing.retrofit.annotation.RetrofitServiceConfig;
import com.shuyun.air.kylin.micronaut.support.discovery.RoundRobinLoadBalancer;
import com.shuyun.apaas.connector.fast.auth.PassportClientAuth;
import com.shuyun.apaas.connector.fast.discovery.LoadBalancerFactory;
import com.shuyun.apaas.connector.fast.interceptor.KylinClientInterceptor;
import com.shuyun.apaas.connector.fast.interceptor.RequestLoggingInterceptor;
import com.shuyun.connector.devkit.kylin.token.delegate.EpassportTokenDelegate;
import com.shuyun.connector.devkit.kylin.token.interceptor.KylinSignInterceptor;
import com.shuyun.connector.devkit.kylin.token.interceptor.KylinTokenInterceptor;
import com.shuyun.connector.devkit.kylin.token.retrofit.EpassportTokenRetrofit;
import com.shuyun.apaas.connector.fast.client.*;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.lite.client.ConfigurationManagerHolder;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.shuyun.apaas.connector.fast.util.ModelUtil;

import java.util.Collections;

@Getter
@Singleton
@Slf4j
public class ClientManager {

    private final BenefitMgmtClient benefitMgmtClient;
    private final BenefitServiceClient benefitServiceClient;
    private final CdpClient cdpClient;
    private final DataSelectorClient dataSelectorClient;
    private final EbrandMemberClient ebrandMemberClient;
    private final EpassportClient epassportClient;
    private final LoyaltyFacadeClient loyaltyFacadeClient;
    private final LoyaltyManagerClient loyaltyManagerClient;
    private final MbspClient mbspClient;
    private final PrepaidCardClient prepaidCardClient;

    @Inject
    public ClientManager(FastConfig config,
                         LoadBalancerFactory loadBalancerFactory,
                         PassportClientAuth passportClient) {

        log.info("~~~~~~~~~~~~~~~~~~~env start~~~~~~~~~~~~~~~~~~~");
        ConfigurationManagerHolder.init();
        log.info("~~~~~~~~~~~~~~~~~~~env end~~~~~~~~~~~~~~~~~~~~~");

//        passportClient.register(config.getClientId());
//
//        ModelUtil.initModel(null, Collections.emptyMap());

        RequestLoggingInterceptor loggingInterceptor = new RequestLoggingInterceptor(config.getKylinRequestLogEnable());

        Boolean clientDiscoveryEnable = config.getClientDiscoveryEnable();
        LcapCallAdapterFactory callAdapterFactory = new LcapCallAdapterFactory();
        KylinSignInterceptor kylinSignInterceptor = new KylinSignInterceptor(config.getCallerKey(), config.getCallerSecret());
        RetrofitServiceConfig epassportConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor),
                callAdapterFactory,
                64, 64, 60, 16, 10);
        EpassportTokenRetrofit epassportRetrofit = RetrofitProxy.INSTANCE.apiProxy(epassportConfig, EpassportTokenRetrofit.class);
        EpassportTokenDelegate epassportDelegate = new EpassportTokenDelegate(config.getClientId(), config.getClientSecret(), config.getEnv(), epassportRetrofit);

        String publicAddress = config.getRequestUrl().startsWith("https://") ? config.getRequestUrl().replace("https://", "") : config.getRequestUrl().replace("http://", "");
        KylinTokenInterceptor kylinTokenInterceptor = new KylinTokenInterceptor(epassportDelegate);

        RetrofitServiceConfig benefitMgmtClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("benefit-mgmt"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        benefitMgmtClient = RetrofitProxy.INSTANCE.apiProxy(benefitMgmtClientConfig, BenefitMgmtClient.class);

        RetrofitServiceConfig benefitServiceClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("benefit-service"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        benefitServiceClient = RetrofitProxy.INSTANCE.apiProxy(benefitServiceClientConfig, BenefitServiceClient.class);

        RetrofitServiceConfig cdpClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("cdp-mgmt"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        cdpClient = RetrofitProxy.INSTANCE.apiProxy(cdpClientConfig, CdpClient.class);

        RetrofitServiceConfig dataSelectorClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("data-selector"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        dataSelectorClient = RetrofitProxy.INSTANCE.apiProxy(dataSelectorClientConfig, DataSelectorClient.class);

        RetrofitServiceConfig ebrandMemberClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("ebrand-member"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        ebrandMemberClient = RetrofitProxy.INSTANCE.apiProxy(ebrandMemberClientConfig, EbrandMemberClient.class);

        RetrofitServiceConfig epassportClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("epassport"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        epassportClient = RetrofitProxy.INSTANCE.apiProxy(epassportClientConfig, EpassportClient.class);

        RetrofitServiceConfig loyaltyFacadeClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("loyalty-facade"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        loyaltyFacadeClient = RetrofitProxy.INSTANCE.apiProxy(loyaltyFacadeClientConfig, LoyaltyFacadeClient.class);

        RetrofitServiceConfig loyaltyManagerClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("loyalty-manager"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        loyaltyManagerClient = RetrofitProxy.INSTANCE.apiProxy(loyaltyManagerClientConfig, LoyaltyManagerClient.class);

        RetrofitServiceConfig mbspClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("mbsp-api"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        mbspClient = RetrofitProxy.INSTANCE.apiProxy(mbspClientConfig, MbspClient.class);

        RetrofitServiceConfig prepaidCardClientConfig = new RetrofitServiceConfig(config.getRequestUrl(),
                Lists.newArrayList(kylinSignInterceptor, kylinTokenInterceptor, loggingInterceptor, new KylinClientInterceptor(clientDiscoveryEnable, publicAddress, (RoundRobinLoadBalancer) loadBalancerFactory.create("prepaid-card-service"))),
                callAdapterFactory,
                64, 64, 60, 32, 10);
        prepaidCardClient = RetrofitProxy.INSTANCE.apiProxy(prepaidCardClientConfig, PrepaidCardClient.class);
    }
}
