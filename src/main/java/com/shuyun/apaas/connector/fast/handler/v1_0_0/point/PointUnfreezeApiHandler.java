package com.shuyun.apaas.connector.fast.handler.v1_0_0.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.PointCache;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.PointService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointUnfreezeParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointUnfreezeRequest;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PointUnfreezeApiHandler extends AbstractApiHandler<PointUnfreezeParam, Void, MemberPointUnfreezeRequest, Void> {

    private final MemberService memberService;
    private final PointService pointService;

    @Inject
    public PointUnfreezeApiHandler(MemberService memberService,
                                   PointService pointService){
        this.memberService = memberService;
        this.pointService = pointService;
    }

    @Override
    public void validate(PointUnfreezeParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public PointUnfreezeParam beforeRequest(PointUnfreezeParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public MemberPointUnfreezeRequest prepareParam(PointUnfreezeParam param) {
        PointCache cache = pointService.bizCacheGet(param, param.getPointBizType());
        MemberPointUnfreezeRequest request = new MemberPointUnfreezeRequest();
        request.setChannelType(param.getRequestChannel());
        request.setMemberId(param.getIdentify().getMemberId());
        request.setActionName(param.getActionName());
        request.setDesc(param.getDescription());
        request.setPointAccountId(cache.getPointAccountTypeId());
        request.setShopId(param.getShopCode());
        request.setBusinessId(param.getFreezeTransactionId());
        request.setKzzd1(param.getKZZD1());
        request.setKzzd2(param.getKZZD2());
        request.setKzzd3(param.getKZZD3());
        return request;
    }

    @Override
    public Void request(MemberPointUnfreezeRequest invokeParam) {
        return pointService.unfreeze(invokeParam);
    }

    @Override
    public Void prepareResult(PointUnfreezeParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_UNFREEZE;
    }
}
