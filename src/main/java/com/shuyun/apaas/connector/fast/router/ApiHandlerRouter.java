package com.shuyun.apaas.connector.fast.router;

import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.activity.ActivityPrizeRecordSyncApiHandler;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.auth.SsoLoginApiHandler;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.grade.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.mdm.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.medal.MedalGetApiHandler;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.point.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.member.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.tag.*;
import com.shuyun.apaas.connector.fast.handler.v1_0_0.trade.*;
import com.shuyun.apaas.connector.fast.tuple.Tuple2;
import com.shuyun.fast.base.ApiTag;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Singleton
public class ApiHandlerRouter implements Router<AbstractApiHandler, ApiTag> {

    private final static ConcurrentHashMap<Tuple2<String, String>, AbstractApiHandler> handlerMap = new ConcurrentHashMap<>();
    private final List<AbstractApiHandler> handlers;

    @Inject
    public ApiHandlerRouter(CouponAvailableListApiHandler couponAvailableListApiHandler,
                            CouponConsumeApiHandler couponConsumeApiHandler,
                            CouponConsumeRepealApiHandler couponConsumeRepealApiHandler,
                            CouponDiscountCalcApiHandler couponDiscountCalcApiHandler,
                            CouponGetApiHandler couponGetApiHandler,
                            CouponGrantApiHandler couponGrantApiHandler,
                            CouponGrantRepealApiHandler couponGrantRepealApiHandler,
                            CouponListApiHandler couponListApiHandler,
                            CouponLockApiHandler couponLockApiHandler,
                            CouponProjectGetApiHandler couponProjectGetApiHandler,
                            CouponProjectListApiHandler couponProjectListApiHandler,
                            CouponReceiveApiHandler couponReceiveApiHandler,
                            CouponReturnApiHandler couponReturnApiHandler,
                            CouponTransferApiHandler couponTransferApiHandler,
                            CouponUnlockApiHandler couponUnlockApiHandler,

                            GradeGetApiHandler gradeGetApiHandler,
                            GradeBudgetApiHandler gradeBudgetApiHandler,
                            GradeRecordsGetApiHandler gradeRecordsGetApiHandler,
                            GradeMetaGetApiHandler gradeMetaGetApiHandler,

                            MedalGetApiHandler medalGetApiHandler,

                            MdmOrgSyncApiHandler mdmOrgSyncApiHandler,
                            MdmProductSyncApiHandler mdmProductSyncApiHandler,
                            MdmShopListApiHandler mdmShopListApiHandler,
                            MdmShopSyncApiHandler mdmShopSyncApiHandler,

                            MemberBindApiHandler memberBindApiHandler,
                            MemberDeregisterApiHandler memberDeregisterApiHandler,
                            MemberDynamicCodeGetApiHandler memberDynamicCodeGetApiHandler,
                            MemberDynamicCodeIdentifyApiHandler memberDynamicCodeIdentifyApiHandler,
                            MemberGetApiHandler memberGetApiHandler,
                            MemberMobileModifyApiHandler memberMobileModifyApiHandler,
                            MemberModifyApiHandler memberModifyApiHandler,
                            MemberRegisterApiHandler memberRegisterApiHandler,
                            MemberUnbindApiHandler memberUnbindApiHandler,

                            PointFreezeApiHandler pointFreezeApiHandler,
                            PointFreezeDeductApiHandler pointFreezeDeductApiHandler,
                            PointGetApiHandler pointGetApiHandler,
                            PointModifyApiHandler pointModifyApiHandler,
                            PointRecordsGetApiHandler pointRecordsGetApiHandler,
                            PointRevertApiHandler pointRevertApiHandler,
                            PointUnfreezeApiHandler pointUnfreezeApiHandler,

                            TagCategoryListHandler tagCategoryListHandler,
                            TagListHandler tagListHandler,
                            TagTaggingHandler tagTaggingHandler,
                            TagUntaggingHandler tagUntaggingHandler,
                            TagUserTagsHandler tagUserTagsHandler,

                            TradeGetApiHandler tradeGetApiHandler,
                            TradeOrderSyncApiHandler tradeOrderSyncApiHandler,
                            TradeRefundSyncApiHandler TradeRefundSyncApiHandler,
                            ActivityPrizeRecordSyncApiHandler activityPrizeRecordSyncApiHandler,
                            SsoLoginApiHandler ssoLoginApiHandler){
        this.handlers = new ArrayList<>();
        handlers.add(couponAvailableListApiHandler);
        handlers.add(couponConsumeApiHandler);
        handlers.add(couponConsumeRepealApiHandler);
        handlers.add(couponDiscountCalcApiHandler);
        handlers.add(couponGetApiHandler);
        handlers.add(couponGrantApiHandler);
        handlers.add(couponGrantRepealApiHandler);
        handlers.add(couponListApiHandler);
        handlers.add(couponLockApiHandler);
        handlers.add(couponProjectGetApiHandler);
        handlers.add(couponProjectListApiHandler);
        handlers.add(couponReceiveApiHandler);
        handlers.add(couponTransferApiHandler);
        handlers.add(couponUnlockApiHandler);
        handlers.add(couponReturnApiHandler);

        handlers.add(gradeGetApiHandler);
        handlers.add(gradeBudgetApiHandler);
        handlers.add(gradeRecordsGetApiHandler);
        handlers.add(gradeMetaGetApiHandler);

        handlers.add(medalGetApiHandler);

        handlers.add(mdmOrgSyncApiHandler);
        handlers.add(mdmProductSyncApiHandler);
        handlers.add(mdmShopListApiHandler);
        handlers.add(mdmShopSyncApiHandler);

        handlers.add(memberBindApiHandler);
        handlers.add(memberDeregisterApiHandler);
        handlers.add(memberDynamicCodeGetApiHandler);
        handlers.add(memberDynamicCodeIdentifyApiHandler);
        handlers.add(memberGetApiHandler);
        handlers.add(memberMobileModifyApiHandler);
        handlers.add(memberModifyApiHandler);
        handlers.add(memberRegisterApiHandler);
        handlers.add(memberUnbindApiHandler);

        handlers.add(pointFreezeApiHandler);
        handlers.add(pointFreezeDeductApiHandler);
        handlers.add(pointGetApiHandler);
        handlers.add(pointModifyApiHandler);
        handlers.add(pointRecordsGetApiHandler);
        handlers.add(pointRevertApiHandler);
        handlers.add(pointUnfreezeApiHandler);

        handlers.add(tagCategoryListHandler);
        handlers.add(tagListHandler);
        handlers.add(tagTaggingHandler);
        handlers.add(tagUntaggingHandler);
        handlers.add(tagUserTagsHandler);

        handlers.add(tradeGetApiHandler);
        handlers.add(tradeOrderSyncApiHandler);
        handlers.add(TradeRefundSyncApiHandler);
        handlers.add(activityPrizeRecordSyncApiHandler);
        handlers.add(ssoLoginApiHandler);

        init();
    }


    @Override
    public void init() {
        Iterator iterator = handlers.iterator();
        while(iterator.hasNext()){
            AbstractApiHandler handler = (AbstractApiHandler)iterator.next();
            String apiVersion = handler.apiVersion();
            String apiName = handler.apiName();
            Tuple2<String, String> apiKey = new Tuple2<>(apiName, apiVersion);
            handlerMap.put(apiKey, handler);
        }

        log.info("api handler mapping success");
    }
    @Override
    public AbstractApiHandler route(ApiTag tag) {
        Tuple2<String, String> apiKey = new Tuple2<>(tag.apiName(), tag.apiVersion());
        return handlerMap.get(apiKey);
    }

}
