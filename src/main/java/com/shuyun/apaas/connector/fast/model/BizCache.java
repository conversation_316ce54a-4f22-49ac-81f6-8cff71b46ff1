package com.shuyun.apaas.connector.fast.model;

import com.shuyun.air.framework.annotation.AttributeSet;
import com.shuyun.air.framework.annotation.Index;
import com.shuyun.air.framework.annotation.ValueConstraint;
import com.shuyun.ticket.domain.BaseEntity;
import com.shuyun.ticket.domain.EntityMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Set;

@AttributeSet(module = "fast-connector", code = "bizCache",
        keys = {@Index(name = "idx_tenantId", unique = false,
                attrs = {BizCache.Fields.tenantId}),
                @Index(name = "idx_bizCode", unique = false,
                        attrs = {BizCache.Fields.bizCode}),
                @Index(name = "idx_cacheType", unique = false,
                        attrs = {BizCache.Fields.cacheType})}, ignore = true)
@FieldNameConstants
@Data
public class BizCache implements BaseEntity {
    public static final Set<String> fieldNames = EntityMapper.fieldNames(BizCache.Fields.class);

    public static String BENEFIT = "benefit";
    public static String GRADE = "grade";
    public static String POINT = "point";
    public static String MBSP = "mbsp";
    public static String SELECTOR = "selector";
    public static String MDM = "mdm";
    public static String TRADE = "trade";
    public static String TAG = "tag";
    public static String CHECKLIST = "checklist";

    @Schema(title = "id", maxLength = 32, required = true)
    @ValueConstraint(maxLen = 32)
    @NotEmpty
    private String id;

    @Schema(title = "租户id", maxLength = 64, required = true)
    @ValueConstraint(nullable = false, maxLen = 64)
    @NotNull
    private String tenantId;

    @Schema(title = "业务编码", maxLength = 64, required = true)
    @ValueConstraint(nullable = false, maxLen = 64)
    @NotNull
    private String bizCode;

    @Schema(title = "缓存类型", maxLength = 64, required = true)
    @ValueConstraint(nullable = false, maxLen = 64)
    @NotNull
    private String cacheType;

    @Schema(title = "缓存值", maxLength = 1024, required = true)
    @ValueConstraint(nullable = false, maxLen = 1024)
    @NotNull
    private String value;

    @Schema(title = "创建时间", required = true)
    @ValueConstraint(nullable = false)
    @NotNull
    private LocalDateTime createTime;

    @Schema(title = "更新时间", required = true)
    @ValueConstraint(nullable = false)
    @NotNull
    private LocalDateTime updateTime;

}
