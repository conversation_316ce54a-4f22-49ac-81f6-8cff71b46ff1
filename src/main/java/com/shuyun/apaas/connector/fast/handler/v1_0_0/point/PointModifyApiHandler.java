package com.shuyun.apaas.connector.fast.handler.v1_0_0.point;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.PointService;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.point.PointModifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class PointModifyApiHandler extends AbstractApiHandler<PointModifyParam, Void, PointModifyParam, Void> {

    private final MemberService memberService;
    private final PointService pointService;

    @Inject
    public PointModifyApiHandler(MemberService memberService,
                                 PointService pointService){
        this.memberService = memberService;
        this.pointService = pointService;
    }

    @Override
    public void validate(PointModifyParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public PointModifyParam beforeRequest(PointModifyParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public PointModifyParam prepareParam(PointModifyParam param) {
        return param;
    }

    @Override
    public Void request(PointModifyParam invokeParam) {
        return pointService.modify(invokeParam);
    }

    @Override
    public Void prepareResult(PointModifyParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_POINT_MODIFY;
    }
}
