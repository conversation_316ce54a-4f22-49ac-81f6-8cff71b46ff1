package com.shuyun.apaas.connector.fast.cache.v1_0_0;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动缓存类
 * 参考其他缓存类实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ActivityCache implements Serializable {
    
    /**
     * 会员类型
     */
    private String memberType;
    
    /**
     * 活动配置信息
     */
    private String activityConfig;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
