package com.shuyun.apaas.connector.fast.handler.v1_0_0.mdm;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MdmService;
import com.shuyun.fast.v1_0_0.param.mdm.MdmProductSyncParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
public class MdmProductSyncApiHandler extends AbstractApiHandler<MdmProductSyncParam, Void, MdmProductSyncParam, Void> {
    private final MdmService mdmService;

    @Inject
    public MdmProductSyncApiHandler(MdmService mdmService){
        this.mdmService = mdmService;
    }

    @Override
    public void validate(MdmProductSyncParam param) {
        super.validate(param);
    }

    @Override
    public MdmProductSyncParam beforeRequest(MdmProductSyncParam param) {
        super.beforeRequest(param);
        param.setId(param.getProductCode());
        param.setChannelType(param.getRequestChannel());
        return param;
    }

    @Override
    public MdmProductSyncParam prepareParam(MdmProductSyncParam param) {
        // TODO: 2024/3/28 时区转换
        return param;
    }

    @Override
    public Void request(MdmProductSyncParam invokeParam) {
        return mdmService.productSync(invokeParam);
    }

    @Override
    public Void prepareResult(MdmProductSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_PRODUCT_SYNC;
    }
}
