package com.shuyun.apaas.connector.fast.model.http.param.activity;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.constant.ModelTags;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 活动领奖记录同步参数类
 * 参考门店同步接口实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
@JsonFilter("activityPrizeRecordSyncParamFilter")
@Schema(title = "活动领奖记录同步参数")
public class ActivityPrizeRecordSyncParam extends ApiBaseParam {

    @Schema(title = "记录ID", maxLength = 64, description = "活动领奖记录的唯一标识，用于数据同步")
    private String id;

    @Schema(title = "活动ID", required = true, maxLength = 64, description = "活动的唯一标识")
    @NotBlank(message = "活动ID不能为空")
    private String actId;

    @Schema(title = "活动名称", required = true, maxLength = 200, description = "活动名称")
    @NotBlank(message = "活动名称不能为空")
    private String actName;

    @Schema(title = "活动产品", required = true, maxLength = 200, description = "活动产品名称")
    @NotBlank(message = "活动产品不能为空")
    private String productName;

    @Schema(title = "用户ID（被邀请）", required = true, maxLength = 64, description = "被邀请用户的ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(title = "麒麟会员id", required = true, maxLength = 64, description = "被邀请用户的ID")
    @NotBlank(message = "麒麟会员id不能为空")
    private String memberId;

    @Schema(title = "昵称（被邀请）", required = true, maxLength = 100, description = "被邀请用户的昵称")
    @NotBlank(message = "昵称不能为空")
    private String nickname;

    @Schema(title = "手机号（被邀请）", required = true, maxLength = 20, description = "被邀请用户的手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(title = "邀请时间", required = true, description = "邀请发生的时间")
    @NotNull(message = "邀请时间不能为空")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private LocalDateTime inviteTime;

    @Schema(title = "邀请人ID", required = true, maxLength = 64, description = "邀请人的ID")
    //@NotBlank(message = "邀请人ID不能为空")
    private String inviterId;

    @Schema(title = "邀请人名字", required = true, maxLength = 100, description = "邀请人的姓名")
    @NotBlank(message = "邀请人名字不能为空")
    private String inviterName;

    @Schema(title = "邀请人手机号", required = true, maxLength = 20, description = "邀请人的手机号")
    @NotBlank(message = "邀请人手机号不能为空")
    private String inviterPhone;

    @Schema(title = "邀请人所属渠道ID", required = true, maxLength = 64, description = "邀请人所属渠道的ID")
    @NotBlank(message = "邀请人所属渠道ID不能为空")
    private String inviterChannelId;

    @Schema(title = "邀请人所属渠道名称", required = true, maxLength = 200, description = "邀请人所属渠道的名称")
    @NotBlank(message = "邀请人所属渠道名称不能为空")
    private String inviterChannelName;

    @Schema(title = "经纬度", maxLength = 100, description = "地理位置的经纬度信息，格式：经度,纬度")
    private String gpsLocation;

    @Schema(title = "省", maxLength = 50, description = "省份名称")
    private String provinceName;

    @Schema(title = "市", maxLength = 50, description = "城市名称")
    private String cityName;

    @Schema(title = "区", maxLength = 50, description = "区县名称")
    private String areaName;

    @Schema(title = "会员类型", maxLength = 32, description = "会员类型，系统自动设置")
    private String memberType;

    @Schema(title = "渠道类型", maxLength = 32, description = "渠道类型，系统自动设置")
    private String channelType;

    /*@Schema(title = "是否有效", maxLength = 10, description = "记录是否有效，Y-有效，N-无效", defaultValue = "Y")
    private String isValid = "Y";*/

    @Schema(title = "最后同步时间", description = "最后同步时间")
    private LocalDateTime lastSync;

    @Schema(title = "扩展字段", description = "扩展字段，用于存储额外的业务数据")
    private Map<String, Object> extension;

    @Override
    public String apiName() {
        return "activity.prizeRecord.sync";
    }
    public String fqn() {
        return ModelTags.DATA_FQN_ACTIVITY_PRIZE_RECORD;
    }
}
