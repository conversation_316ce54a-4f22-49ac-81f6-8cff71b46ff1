package com.shuyun.apaas.connector.fast.client;

import com.shuyun.ticket.benefit.domain.BenefitChangeLog;
import com.shuyun.ticket.benefit.vo.request.benefit.*;
import com.shuyun.ticket.benefit.vo.response.benefit.*;
import com.shuyun.ticket.benefit.vo.response.benefit.batchImport.BenefitBatchImportResponse;
import retrofit2.http.*;
import io.swagger.v3.oas.annotations.Operation;
import javax.annotation.Nullable;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import okhttp3.RequestBody;

public interface BenefitServiceClient {

    @Operation(summary = "查询实例详情")
    @GET("/benefit-service/v1/api/domain/benefit")
    BenefitResponse instanceById(
            @Query(value = "_i18n_query_") String i18nQuery,
            @Query(value = "programId") String programId,
            @Nullable @Query("id") String instanceId,
            @Query("code") String code,
            @Query("subjectFqn") String subjectFqn,
            @Query("projectId") String projectId);

    @Operation(summary = "查询卡券实例变更记录日志")
    @GET("/benefit-service/v1/internal/domain/benefit/change/log/{benefitId}")
    List<BenefitChangeLog> changeLog(
            @Path(value = "benefitId") String benefitId,
            @Query(value = "_i18n_query_") String i18nQuery,
            @Query(value = "subjectId") String subjectId,
            @Query(value = "projectId") String projectId,
            @Query(value = "pageNum") Integer pageNum,
            @Query(value = "pageSize") Integer pageSize);

    @Operation(summary = "卡券列表查询")
    @GET("/benefit-service/v1/api/domain/benefit/list")
    List<BenefitResponse> list(
            @Query(value = "_i18n_query_") String i18nQuery,
            @Query(value = "programId") String programId,
            @Query(value = "subjectFqn") String subjectFqn,
            @Nullable @Query(value = "projectId") String projectId,
            @Query(value = "holder") String holder,
            @Nullable @Query(value = "state") String state,
            @Nullable @Query(value = "expireTime") LocalDateTime expireTime,
            @Nullable @Query(value = "queryParam") String queryParam,
            @Query(value = "pageNum") Integer pageNum,
            @Query(value = "pageSize") Integer pageSize);

    @Operation(summary = "卡券实例列表查询(分页)")
    @POST("/benefit-service/v1/api/domain/benefit/page")
    BenefitDynamicResponse<BenefitResponse> pageList(@Valid @Body BenefitQueryListRequest request);

    @Operation(summary = "卡券实例列表总数查询(分页)")
    @POST("/benefit-service/v1/api/domain/benefit/page/total")
    BenefitDynamicResponse<Object> pageListTotal(@Valid @Body BenefitQueryListRequest request);

    @Operation(summary = "可用卡券列表")
    @POST("/benefit-service/v1/api/domain/benefit/scene/available_list?_i18n_query_=translate")
    BenefitAvailableListResponse availableList(@Valid @Body BenefitAvailableListRequest request);

    @Operation(summary = "创建")
    @POST("/benefit-service/v1/api/domain/benefit/scene/create")
    BenefitGrantResponse create(@Valid @Body BenefitCreateRequest request);

    @Operation(summary = "发放")
    @POST("/benefit-service/v1/api/domain/benefit/scene/grant")
    BenefitGrantResponse grant(@Valid @Body BenefitGrantRequest request);

    @Operation(summary = "启用")
    @POST("/benefit-service/v1/api/domain/benefit/scene/activate")
    BenefitActivateResponse activate(@Valid @Body BenefitActivateRequest request);

    @Operation(summary = "停用")
    @POST("/benefit-service/v1/api/domain/benefit/scene/deactivate")
    BenefitDeactivateResponse deactivate(@Valid @Body BenefitDeactivateRequest request);

    @Operation(summary = "使用校验")
    @POST("/benefit-service/v1/api/domain/benefit/scene/check_use")
    BenefitUseResponse checkUse(@Valid @Body BenefitUseRequest request);

    @Operation(summary = "核销")
    @POST("/benefit-service/v1/api/domain/benefit/scene/use")
    BenefitUseResponse use(@Valid @Body BenefitUseRequest request);

    @Operation(summary = "反核销")
    @POST("/benefit-service/v1/api/domain/benefit/scene/cancel_use")
    BenefitCancelUseResponse cancelUse(@Valid @Body BenefitCancelUseRequest request);

    @Operation(summary = "优惠金额计算")
    @POST("/benefit-service/v1/api/domain/benefit/scene/discount_calc")
    BenefitDiscountCalcResponse discountCalc(@Valid @Body BenefitDiscountCalcRequest request);

    @Operation(summary = "锁定")
    @POST("/benefit-service/v1/api/domain/benefit/scene/lock")
    BenefitLockResponse lock(@Valid @Body BenefitLockRequest request);

    @Operation(summary = "解锁")
    @POST("/benefit-service/v1/api/domain/benefit/scene/unlock")
    BenefitUnlockResponse unLock(@Valid @Body BenefitUnlockRequest request);

    @Operation(summary = "转赠")
    @POST("/benefit-service/v1/api/domain/benefit/scene/transfer")
    BenefitTransferResponse transfer(@Valid @Body BenefitTransferRequest request);

    @Operation(summary = "受赠")
    @POST("/benefit-service/v1/api/domain/benefit/scene/receive")
    BenefitReceiveResponse receive(@Valid @Body BenefitReceiveRequest request);

    @Operation(summary = "退回")
    @POST("/benefit-service/v1/api/domain/benefit/scene/return")
    BenefitReturnResponse returns(@Valid @Body BenefitReturnRequest request);

    @Operation(summary = "绑定")
    @POST("/benefit-service/v1/api/domain/benefit/scene/bind")
    BenefitBindResponse bind(@Valid @Body BenefitBindRequest request);

    @Operation(summary = "解绑")
    @POST("/benefit-service/v1/api/domain/benefit/scene/unbind")
    BenefitUnbindResponse unbind(@Valid @Body BenefitUnbindRequest request);

    @Operation(summary = "变更有效期")
    @POST("/benefit-service/v1/api/domain/benefit/scene/change_effective")
    BenefitChangeEffectiveResponse changeEffective(@Valid @Body BenefitChangeEffectiveRequest request);

    @Operation(summary = "生效")
    @POST("/benefit-service/v1/api/domain/benefit/scene/effective")
    BenefitEffectiveResponse effective(@Valid @Body BenefitEffectiveRequest request);

    @Operation(summary = "失效")
    @POST("/benefit-service/v1/api/domain/benefit/scene/expire")
    BenefitExpireResponse expire(@Valid @Body BenefitExpireRequest request);


    @Operation(summary = "批量导入实例")
    @POST("/benefit-service/v1/api/domain/benefit/import/batch")
    BenefitBatchImportResponse importBatch(@Body RequestBody request);

    @Operation(summary = "合卡")
    @POST("/benefit-service/v1/api/domain/benefit/scene/merge")
    BenefitMergeResponse mergeBenefit(@Valid @Body BenefitMergeRequest request);


    @Operation(summary = "指定主体作废")
    @POST("/benefit-service/v1/api/domain/benefit/scene/discard_subject")
    BenefitDiscardResponse discardBenefitsBySubject(@Valid @Body BenefitDiscardSubjectRequest request);

    @Operation(summary = "冻结")
    @POST("/benefit-service/v1/api/domain/benefit/scene/freeze")
    BenefitFreezeResponse freeze(@Valid @Body BenefitFreezeRequest request);

    @Operation(summary = "解冻")
    @POST("/benefit-service/v1/api/domain/benefit/scene/unfreeze")
    BenefitUnfreezeResponse unFreeze(@Valid @Body BenefitUnfreezeRequest request);

    @Operation(summary = "作废")
    @POST("/benefit-service/v1/api/domain/benefit/scene/discard")
    BenefitDiscardResponse discard(@Valid @Body BenefitDiscardRequest request);
    
}
