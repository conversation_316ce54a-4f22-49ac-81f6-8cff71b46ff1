package com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.BenefitService;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.param.coupon.CouponConsumeRepealParam;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitCancelUseRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitCancelUseResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class CouponConsumeRepealApiHandler extends AbstractApiHandler<CouponConsumeRepealParam, Void, BenefitCancelUseRequest, BenefitCancelUseResponse> {

    private final BenefitService benefitService;
    private final MemberService memberService;

    @Inject
    public CouponConsumeRepealApiHandler(MemberService memberService,
                                         BenefitService benefitService){
        this.memberService = memberService;
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponConsumeRepealParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        if(Objects.nonNull(identify)){
            String memberId = identify.getMemberId();
            String userId = identify.getUserId();
            FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
        }
    }

    @Override
    public CouponConsumeRepealParam beforeRequest(CouponConsumeRepealParam param) {
        super.beforeRequest(param);
        if(Objects.nonNull(param.getIdentify())){
            param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        }
        return param;
    }

    @Override
    public BenefitCancelUseRequest prepareParam(CouponConsumeRepealParam param) {
        BenefitCancelUseRequest request = benefitService.setSceneParam(new BenefitCancelUseRequest(), param);
        if(Objects.nonNull(param.getIdentify())){
            request.setUser(param.getIdentify().getMemberId());
        }
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        request.setUseTransactionId(param.getUseTransactionId());
        return request;
    }

    @Override
    public BenefitCancelUseResponse request(BenefitCancelUseRequest invokeParam) {
        return benefitService.consumeRepeal(invokeParam);
    }

    @Override
    public Void prepareResult(CouponConsumeRepealParam param, BenefitCancelUseResponse result) {
        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            return null;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500231, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_CONSUME_REPEAL;
    }
}
