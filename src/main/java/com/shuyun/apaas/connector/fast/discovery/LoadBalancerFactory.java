package com.shuyun.apaas.connector.fast.discovery;

import com.shuyun.air.kylin.micronaut.support.discovery.RoundRobinLoadBalancer;
import com.shuyun.air.kylin.micronaut.support.discovery.SpectrumDiscoveryClient;
import io.micronaut.discovery.DiscoveryClient;
import io.micronaut.http.client.LoadBalancer;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Singleton
public class LoadBalancerFactory {
    private static final Map<String, LoadBalancer> CACHE = new ConcurrentHashMap<>();

    private final SpectrumDiscoveryClient discoveryClient;
    @Inject
    public LoadBalancerFactory() {
        discoveryClient = new SpectrumDiscoveryClient();
    }

    public LoadBalancer create(String serviceID) {
        return CACHE.computeIfAbsent(serviceID, key -> new RoundRobinLoadBalancer(serviceID, (SpectrumDiscoveryClient)getDiscoveryClient()));
    }

    public DiscoveryClient getDiscoveryClient() {
        return discoveryClient;
    }
}
