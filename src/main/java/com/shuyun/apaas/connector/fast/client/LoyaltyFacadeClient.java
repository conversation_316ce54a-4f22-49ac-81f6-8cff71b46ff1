package com.shuyun.apaas.connector.fast.client;

import com.shuyun.fast.v1_0_0.domain.Point;
import com.shuyun.loyalty.sdk.api.model.http.PlanResponse;
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeModifyRequest;
import com.shuyun.loyalty.sdk.api.model.http.points.*;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import retrofit2.http.*;

public interface LoyaltyFacadeClient {
    @Operation(summary = "积分账户初始化")
    @POST("/loyalty-facade/v1/point:init")
    MemberPointInitResponse init(@Valid @Body MemberPointInitRequest request);

    @Operation(summary = "增加会员积分")
    @POST("/loyalty-facade/v1/point:send")
    MemberPointSendResponse send(@Valid @Body MemberPointSendRequest request);

    @Operation(summary = "扣减会员积分")
    @POST("/loyalty-facade/v1/point:deduct")
    Void deduct(@Valid @Body MemberPointDeductRequest request);

    @Operation(summary = "允许负积分扣减(当单抵现场景)")
    @POST("/loyalty-facade/v1/point:allowedNegativeDetect")
    Void allowedNegativeDeduct(@Valid @Body MemberPointDeductRequest request);

    @Operation(summary = "冻结会员积分")
    @POST("/loyalty-facade/v1/point:frozen")
    Void frozen(@Valid @Body MemberPointFreezeRequest request);

    @Operation(summary = "解冻会员积分")
    @POST("/loyalty-facade/v1/point:unfrozen")
    Void unfrozen(@Valid @Body MemberPointUnfreezeRequest request);

    @Operation(summary = "消耗已冻结会员积分")
    @POST("/loyalty-facade/v1/point:frozenDeduct")
    Void  frozenDeduct(@Valid @Body MemberPointUseFrozenRequest request);

    @Operation(summary = "会员积分查询")
    @GET("/loyalty-facade/v1/point")
    Point queryPoint(@Query("memberId") @NotNull String memberId,
                     @Query("pointAccountId") long pointAccountId);

    @Operation(summary = "积分变更撤销")
    @POST("/loyalty-facade/v1/point:revert")
    Void revert(@Valid @Body MemberPointRevertRequest request);

    @Operation(summary = "等级变更")
    @POST("/loyalty-facade/v1/grade/modify")
    Void gradeModify(@Valid @Body MemberGradeModifyRequest request);

    @Operation(summary = "计划方案信息")
    @GET("/loyalty-facade/v1/plan:info")
    PlanResponse planInfo(@Query("planId") @NotNull Long planId);

}
