package com.shuyun.apaas.connector.fast.kafka;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.fast.v1_0_1.param.trade.TradeOrderSyncParam;
import com.shuyun.fast.v1_0_1.param.trade.TradeRefundSyncParam;
import com.shuyun.lite.util.Common;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import java.util.Properties;

@Singleton
@Slf4j
public class KafkaSender<T> {

    private final Producer<String, T> producer;

    private final static ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
    }

    @Inject
    public KafkaSender(FastConfig config){
        Properties props = new Properties();
        String address = config.getKafka().getAddress();
        if(StringUtils.isEmpty(address)){
            address = Common.getSysOrEnv("kafka.address");
        }
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, address);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 0);
        producer = new KafkaProducer<>(props);
    }

    public <T> void send(String topic, String key, T body){
        try {
            String value;
            if(body instanceof TradeOrderSyncParam){
                SimpleBeanPropertyFilter filter = SimpleBeanPropertyFilter.serializeAllExcept("extension");
                SimpleFilterProvider filterProvider = new SimpleFilterProvider();
                filterProvider.addFilter("tradeOrderFilter", filter);
                filterProvider.addFilter("tradeOrderItemFilter", filter);
                value = objectMapper.writer(filterProvider).writeValueAsString(body);
            } else if(body instanceof TradeRefundSyncParam){
                SimpleBeanPropertyFilter filter = SimpleBeanPropertyFilter.serializeAllExcept("extension");
                SimpleFilterProvider filterProvider = new SimpleFilterProvider();
                filterProvider.addFilter("tradeRefundFilter", filter);
                filterProvider.addFilter("tradeRefundOrderItemFilter", filter);
                value = objectMapper.writer(filterProvider).writeValueAsString(body);
            } else{
                value = objectMapper.writeValueAsString(body);
            }
            producer.send(new ProducerRecord(topic, key, value));
        } catch (JsonProcessingException e){
            log.error("kafka producer send error", e);
        }
    }
}
