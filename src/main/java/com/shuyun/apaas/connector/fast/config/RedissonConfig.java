package com.shuyun.apaas.connector.fast.config;

import com.shuyun.apaas.cnc.api.annotations.media.Schema;
import lombok.Data;

@Schema(title = "redis配置")
@Data
public class RedissonConfig {

    @Schema(title = "是否启动redis:默认开启", defaultValue = "true")
    private Boolean enable = true;
    @Schema(title = "是否开启ssl:默认false", defaultValue = "false")
    private String ssl = "false";
    @Schema(title = "address:默认从配置中心读取(可替换)")
    private String address;
    @Schema(title = "password:默认从配置中心读取(可替换)")
    private String password;
    @Schema(title = "poolSize:默认值64", defaultValue = "64")
    private String poolSize = "64";
    @Schema(title = "database:默认值0", defaultValue = "0")
    private String database = "0";
    @Schema(title = "nettyThread:默认值64", defaultValue = "64")
    private int nettyThread = 64;

}
