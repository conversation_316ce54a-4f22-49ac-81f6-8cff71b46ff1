package com.shuyun.apaas.connector.fast.handler.api;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.util.MessageSourceUtils;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTag;
import com.shuyun.fast.base.ApiTags;
import lombok.extern.slf4j.Slf4j;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 本类主要定义业务流转,做一些诸如限流类的公共逻辑控制，尽量不侵入接口业务逻辑
 * @param <P> param:api接口入参
 * @param <R> result:api接口响应
 * @param <IP> invoke param:rpc接口入参
 * @param <IR> invoke result:rpc接口响应
 */
@Slf4j
public abstract class AbstractApiHandler<P, R, IP, IR> implements ApiTag{

    public void validate(P param) {
        Set<ConstraintViolation<Object>> constraintViolations = FastValidator.constraintValidate(param);
        if(!constraintViolations.isEmpty()) {
            throw new ConstraintViolationException(constraintViolations);
        }
    }

    // TODO: 2024/3/5 限流检查、memberId替换等公共事项
    public P beforeRequest(P param) {

        return param;

    }

    public abstract IP prepareParam(P param);

    public abstract IR request(IP invokeParam);

    public abstract R prepareResult(P param, IR result);

    public ApiResult handle(P param) {
        try {
            return ApiResult.success(doHandle(param));
        } catch (Throwable ex){
            if(ex instanceof ConstraintViolationException){
                String code = String.format("%6d", ApiTags.API_RESP_CODE_100001);
                String message = MessageSourceUtils.getMessage(code, ex.getLocalizedMessage());
                return ApiResult.failure(code, message);
            }else if(ex instanceof ApiException){
                ApiException apiException = (ApiException) ex;
                String code = String.format("%6d", apiException.getCode());
                String message = MessageSourceUtils.getMessage(code, apiException.getVariables());
                return ApiResult.failure(code, message);
            }else{
                String code = String.format("%6d", ApiTags.API_RESP_CODE_500100);
                String message = MessageSourceUtils.getMessage(code, ex.getLocalizedMessage());
                return ApiResult.failure(code, message);
            }
//            HttpClientResponseException cre = (HttpClientResponseException)exception;
//            MbspErrorResult errBody = cre.getResponse().getBody(MbspErrorResult.class).orElse(null);
        }
    }

    public R doHandle(P param) {
        validate(param);
        P replacedParam = beforeRequest(param);
        return prepareResult(replacedParam, request(prepareParam(replacedParam)));
    }
}
