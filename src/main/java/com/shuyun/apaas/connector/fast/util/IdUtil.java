package com.shuyun.apaas.connector.fast.util;

import java.util.UUID;
import com.fasterxml.uuid.Generators;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class IdUtil {

    public static String uuid(){
        UUID uuid = Generators.timeBasedGenerator().generate();
        return uuid.toString().replace("-", "");
    }

    public static void main(String[] args){
        log.info(IdUtil.uuid());
    }
}
