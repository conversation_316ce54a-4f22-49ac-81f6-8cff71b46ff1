package com.shuyun.apaas.connector.fast.model.http.result.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 单点登录响应类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Schema(title = "单点登录响应")
public class SsoLoginResult {

    @Schema(title = "用户ID", description = "用户的唯一标识")
    private String id;

    @Schema(title = "用户类型", description = "用户类型")
    private String type;

    @Schema(title = "真实姓名", description = "用户真实姓名")
    private String realname;

    @Schema(title = "用户名", description = "用户名")
    private String username;

    @Schema(title = "员工编号", description = "员工编号")
    private String employeeNo;

    @Schema(title = "是否启用", description = "用户是否启用")
    private Boolean enabled;

    @Schema(title = "是否锁定", description = "用户是否锁定")
    private Boolean locked;

    @Schema(title = "手机号", description = "用户手机号")
    private String mobile;

    @Schema(title = "角色列表", description = "用户拥有的角色列表")
    private List<RoleInfo> roles;

    /**
     * 创建成功响应
     */
    public static SsoLoginResult success(String id, String type, String realname, String username) {
        SsoLoginResult result = new SsoLoginResult();
        result.setId(id);
        result.setType(type);
        result.setRealname(realname);
        result.setUsername(username);
        return result;
    }
}
