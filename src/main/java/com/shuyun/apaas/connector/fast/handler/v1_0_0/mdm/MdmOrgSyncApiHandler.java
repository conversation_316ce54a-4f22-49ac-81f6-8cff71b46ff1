package com.shuyun.apaas.connector.fast.handler.v1_0_0.mdm;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MdmService;
import com.shuyun.fast.v1_0_0.param.mdm.MdmOrgSyncParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class MdmOrgSyncApiHandler extends AbstractApiHandler<MdmOrgSyncParam, Void, MdmOrgSyncParam, Void> {
    private final MdmService mdmService;

    @Inject
    public MdmOrgSyncApiHandler(MdmService mdmService){
        this.mdmService = mdmService;
    }

    @Override
    public void validate(MdmOrgSyncParam param) {
        super.validate(param);
    }


    @Override
    public MdmOrgSyncParam beforeRequest(MdmOrgSyncParam param) {
        super.beforeRequest(param);
        param.setId(param.getOrgCode());
        param.setOrgId(param.getOrgCode());
        param.setParentId(param.getOrgParentCode());
        return param;
    }


    @Override
    public MdmOrgSyncParam prepareParam(MdmOrgSyncParam param) {
        // TODO: 2024/3/28 时区转换
        return param;
    }

    @Override
    public Void request(MdmOrgSyncParam invokeParam) {
        return mdmService.orgSync(invokeParam);
    }

    @Override
    public Void prepareResult(MdmOrgSyncParam param, Void result) {
        return result;
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MDM_ORG_SYNC;
    }
}
