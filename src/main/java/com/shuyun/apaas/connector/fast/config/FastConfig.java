package com.shuyun.apaas.connector.fast.config;

import com.shuyun.apaas.cnc.api.Config;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.api.annotations.media.Schema;
import com.shuyun.apaas.cnc.api.enums.UITypeEnum;
import lombok.Data;

@Title("fast连接器配置属性")
@Data
public class FastConfig implements Config {

    @Schema(title = "麒麟内部请求是否记录日志:默认关闭", defaultValue = "false")
    private Boolean kylinRequestLogEnable = false;

    @Schema(title = "请求的kylin url的地址(用http://开头)", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 200)
    private String requestUrl;

    @Schema(title = "请求的签名key", requiredMode = Schema.RequiredMode.REQUIRED)
    private String callerKey;

    @Schema(title = "请求的签名秘钥", requiredMode = Schema.RequiredMode.REQUIRED)
    private String callerSecret;

    @Schema(title = "请求的kylin客户端id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientId;

    @Schema(title = "请求的kylin客户端密钥", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientSecret;

    @Schema(title = "请求的kylin环境", requiredMode = Schema.RequiredMode.REQUIRED)
    private String env;

    @Schema(title = "是否开启会员通:默认开启(不需要的项目报错后再关闭)", defaultValue = "true")
    private Boolean ebrandEnable = true;

    @Schema(title = "是否开启麒麟客户端服务发现:默认开启", defaultValue = "false")
    private Boolean clientDiscoveryEnable = false;

    @Schema(title = "dataapi配置", uiType = UITypeEnum.GROUP)
    private DataapiConfig dataapi;

    @Schema(title = "eventService配置", uiType = UITypeEnum.GROUP)
    private EventServiceConfig eventService;

    @Schema(title = "benefit配置", uiType = UITypeEnum.GROUP)
    private BenefitConfig benefit;

    @Schema(title = "redis配置", uiType = UITypeEnum.GROUP)
    private RedissonConfig redis;

    @Schema(title = "kafka配置", uiType = UITypeEnum.GROUP)
    private KafkaConfig kafka;
}
