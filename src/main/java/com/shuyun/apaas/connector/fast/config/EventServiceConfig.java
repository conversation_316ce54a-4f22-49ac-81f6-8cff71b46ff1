package com.shuyun.apaas.connector.fast.config;

import com.shuyun.apaas.cnc.api.annotations.media.Schema;
import lombok.Data;
@Schema(title = "eventService配置")
@Data
public class EventServiceConfig {

    @Schema(title = "事件服务url", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serverUrl;
    @Schema(title = "调用事件服务caller", requiredMode = Schema.RequiredMode.REQUIRED)
    private String caller;
    @Schema(title = "调用事件服务secret", requiredMode = Schema.RequiredMode.REQUIRED)
    private String secret;
    @Schema(title = "事件服务userName", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userName;
    @Schema(title = "调用事件服务userSecret", requiredMode = Schema.RequiredMode.REQUIRED)
    private String userSecret;

}
