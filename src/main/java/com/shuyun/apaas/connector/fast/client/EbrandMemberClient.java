package com.shuyun.apaas.connector.fast.client;

import com.shuyun.fast.douyin.param.DoudianSyncRequest;
import com.shuyun.fast.douyin.result.EbrandApiResponse;
import com.shuyun.fast.taobao.param.SyncRequest;
import com.shuyun.fast.taobao.result.SyncResponse;
import com.shuyun.fast.v1_0_0.param.member.MobileEncryptParam;
import com.shuyun.fast.v1_0_0.result.MobileEncryptResult;
import retrofit2.http.*;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.Valid;

public interface EbrandMemberClient {
    @Operation(summary = "明文手机号加密")
    @POST("/ebrand-member/v1/mobile/encryption")
    MobileEncryptResult encrypt(@Valid @Body MobileEncryptParam request);

    @Operation(summary = "抖店会员信息同步", description = "应用场景: 当用户在商家非抖音平台信息发生变更时,麒麟CRM会调此接口同步会员信息给抖店抖店会员通")
    @POST("/ebrand-member/v1/doudian/api/member/batchUpdate")
    EbrandApiResponse douyinSync(@Body DoudianSyncRequest request);

    @Operation(summary = "线下会员信息同步淘宝会员通", description = "应用场景: 用于三方服务将线下会员积分/等级同步到淘宝线上")
    @POST("/ebrand-member/v1/tmall/api/sync")
    SyncResponse taobaoSync(@Body SyncRequest request);
}
