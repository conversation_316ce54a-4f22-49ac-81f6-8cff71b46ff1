package com.shuyun.apaas.connector.fast.cache;

import com.github.benmanes.caffeine.cache.*;

import java.util.*;
import java.util.function.Supplier;

public class SyncCache {

    private final Cache<Object, Object> cache;

    public SyncCache(Cache<Object, Object> cache) {
        this.cache = cache;
    }

    public Cache<Object, Object> getNativeCache() {
        return this.cache;
    }

    public Object get(Object key) {
        return this.cache.getIfPresent(key);
    }
    public void invalidate(Object key) {
        this.cache.invalidate(key);
    }

    public void invalidateAll() {
        this.cache.invalidateAll();
    }

    public void put(Object key, Object value) {
        if (value == null) {
            this.cache.invalidate(key);
        } else {
            this.cache.put(key, value);
        }

    }

    public <T> Optional<T> putIfAbsent(Object key, T value) {
        Object previous = this.cache.asMap().putIfAbsent(key, value);
        return Optional.ofNullable((T)previous);
    }

    public <T> T putIfAbsent(Object key, Supplier<T> value) {
        Object val = this.cache.asMap().computeIfAbsent(key, (k) -> {
            return value.get();
        });
        return (T)val;
    }

}
