package com.shuyun.apaas.connector.fast.util;

import io.micronaut.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Slf4j
public class DateUtil {



    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

//    忠诚度出参时间格式及样例 ZonedDateTime
//    "effectiveDate": "2024-03-06T16:10:59.409+08:00",
//    "overdueDate": "2025-12-31T23:59:59.409+08:00",
//    "created": "2024-03-06T16:10:59.489+08:00", 转换为 2024-03-06 16:10:59


//     忠诚度入参时间格式及样例 ZonedDateTime
//    实际需要入参 "startTime": "2024-03-06T08:10:59.489Z", 0时区  实际变更时间2024-03-06 16:10:59
//     "overdueDate": "2024-03-06T08:10:59.489Z", 0时区

    public static String loyaltyOutputTimeConvert(ZonedDateTime time, String pattern){
        if(Objects.isNull(time)){
            return null;
        }
        pattern = StringUtils.isEmpty(pattern)?"yyyy-MM-dd HH:mm:ss":pattern;
        // 假设已经有了一个ZonedDateTime对象，例如从Hadoop或Spark返回的日期时间
//        ZonedDateTime zonedDateTimeInOtherTimeZone = ZonedDateTime.now(ZoneId.of("UTC")); // 举例：假设这是UTC时区的时间
        // 将其转换为北京时间（中国标准时间）
        ZonedDateTime zonedDateTimeInBeijing = time.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        // 格式化为字符串输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        String result = zonedDateTimeInBeijing.format(formatter);
        System.out.println(result);
        return result;
    }

    //"startTime": "2024-03-06T08:10:59.489Z"
    public static String loyaltyInputTimeConvert(String time, String pattern){
        if(StringUtils.isEmpty(time)){
            return null;
        }
        pattern = StringUtils.isEmpty(pattern)?"yyyy-MM-dd HH:mm:ss":pattern;
        // 定义一个日期时间格式器，根据实际字符串格式来创建
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 创建一个没有时区信息的LocalDateTime对象
        LocalDateTime localDateTime = LocalDateTime.parse(time, formatter);
        // 将其与亚洲/上海时区关联起来，生成ZonedDateTime对象
        ZonedDateTime zonedDateTimeInBeijing = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        // 将时区转换为UTC时区
        ZonedDateTime utcTime = zonedDateTimeInBeijing.withZoneSameInstant(ZoneId.of("UTC"));
        // UTC时间格式
        DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        return utcTime.format(utcFormatter);
    }


//     卡券出参时间格式及样例 LocalDateTime  0时区需转换8时区
//    "insertAt": "2024-03-20T08:17:25.118Z" 实际时间2024-03-20 16:16:34
//     "createAt": "2024-03-20T08:17:18.838Z",
//    卡券入参时间格式及样例,8时区格式不用转换
//    "queryParams": "{\"updateAt\":{\"BETWEEN\":[\"2024-03-20 16:16:34\",\"2024-03-20 16:16:35\"]}}"

    public static String benefitOutputTimeConvert(LocalDateTime time, String pattern){
        if(Objects.isNull(time)){
            return null;
        }
        pattern = StringUtils.isEmpty(pattern)?"yyyy-MM-dd HH:mm:ss":pattern;
        // LocalDateTime对象
//        LocalDateTime localDateTime = LocalDateTime.now(); // 举例：获取当前时间
        // 将其转换为UTC时区的ZonedDateTime
        ZonedDateTime utcTime = time.atZone(ZoneId.of("UTC"));
        // 将其转换为北京时区的ZonedDateTime
        ZonedDateTime  zonedDateTimeInBeijing = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        // 定义一个日期时间格式器以指定输出格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化为字符串
        return zonedDateTimeInBeijing.format(formatter);
    }

    public static String benefitInputTimeConvert(String time, String pattern){
        return time;
    }

//     会籍出参时间格式及样例 String  0时区需转换8时区
//    "enrollTime": "2024-03-20T07:41:25.398Z" 实际时间2024-03-20 15:41:25
//    会籍入参时间格式及样例 ZonedDateTime
//    "queryParams": "{\"updateAt\":{\"BETWEEN\":[\"2024-03-20 16:16:34\",\"2024-03-20 16:16:35\"]}}"


    //会籍出参时间转换
    public static String mbspOutputTimeConvert(String utcLocalDateTimeStr, String pattern){
        if(StringUtils.isEmpty(utcLocalDateTimeStr)){
            return null;
        }
        DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        // LocalDateTime对象
        LocalDateTime localDateTime = LocalDateTime.parse(utcLocalDateTimeStr, utcFormatter); // 举例：获取当前时间
        // 将其转换为UTC时区的ZonedDateTime
        pattern = StringUtils.isEmpty(pattern)?"yyyy-MM-dd HH:mm:ss":pattern;
        ZonedDateTime utcTime = localDateTime.atZone(ZoneId.of("UTC"));
        // 将其转换为北京时区的ZonedDateTime
        ZonedDateTime  zonedDateTimeInBeijing = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        // 定义一个日期时间格式器以指定输出格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化为字符串
        return zonedDateTimeInBeijing.format(formatter);
    }

    //会籍入参时间转换
    public static ZonedDateTime mbspInputTimeConvert(String time, String pattern){
        if(StringUtils.isEmpty(time)){
            return null;
        }
        pattern = StringUtils.isEmpty(pattern)?"yyyy-MM-dd HH:mm:ss":pattern;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // LocalDateTime 北京时区
        LocalDateTime localDateTime = LocalDateTime.parse(time, formatter); // 举例：获取当前时间
        // 将其转换为北京时区的ZonedDateTime
        ZonedDateTime beijingZonedTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        // 将其转换为UTC时区的ZonedDateTime
        return beijingZonedTime.withZoneSameInstant(ZoneId.of("UTC"));
    }


    public static String currentTime() {
        LocalDateTime localDateTime = LocalDateTime.now();
        return formatter.format(localDateTime);
    }

    public static ZonedDateTime utcTime(LocalDateTime localDateTime){
        if(Objects.isNull(localDateTime)){
            return null;
        }
        //亚洲/上海时区ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        //UTC时间
        ZonedDateTime utcTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        return utcTime;
    }


    public static String utcTimeStr(LocalDateTime localDateTime, String pattern){
        if(Objects.isNull(localDateTime)){
            return null;
        }
        //亚洲/上海时区ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        //UTC时间
        ZonedDateTime utcTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
        DateTimeFormatter utcFormatter = DateTimeFormatter.ofPattern(pattern);

        return utcFormatter.format(utcTime);
    }

    public static LocalDateTime localTime(String utcTimeStr){
        if(StringUtils.isEmpty(utcTimeStr)){
            return null;
        }
        ZonedDateTime utcDateTime = ZonedDateTime.parse(utcTimeStr, utcFormatter);
        ZonedDateTime zonedDateTime = utcDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        LocalDateTime localTime = zonedDateTime.toLocalDateTime();
        return localTime;
    }

    public static LocalDateTime localTime(ZonedDateTime utcTime){
        if(Objects.isNull(utcTime)){
            return null;
        }
        ZonedDateTime zonedDateTime = utcTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        LocalDateTime localTime = zonedDateTime.toLocalDateTime();
        return localTime;
    }

    public static LocalDateTime localTime(LocalDateTime utcTime){
        if(Objects.isNull(utcTime)){
            return null;
        }
        return utcTime.plusHours(8);
    }

    public static void main(String[] args){
        ZonedDateTime zonedDateTime = ZonedDateTime.of(LocalDateTime.now().minusHours(8), ZoneId.of("UTC"));
        log.info("local time:{}", DateUtil.localTime(zonedDateTime));

        System.out.println(DateUtil.utcTimeStr(LocalDateTime.now(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
        log.info(DateUtil.utcTimeStr(LocalDateTime.now(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
//        log.info(DateUtil.loyaltyOutputTimeConvert(zonedDateTime, null));
//        log.info(DateUtil.loyaltyInputTimeConvert(null, null));
//        log.info(DateUtil.benefitOutputTimeConvert(null, null));
//        log.info(DateUtil.mbspOutputTimeConvert("2024-03-20T07:41:25.398Z", null));
//        log.info(DateUtil.mbspInputTimeConvert("2024-03-20 16:41:25", null).toString());

//        MemberModifyParam param = new MemberModifyParam();
//        param.setModifyTime("2021-12-30 16:24:02");
//        MemberEditRequest request = JsonUtil.filterConvert(param, MemberEditRequest.class, "jsonParamFilter", "modifyTime");
//        request.setModifyTime(DateUtil.mbspInputTimeConvert(param.getModifyTime(), null));
//        log.info(JsonUtil.serialize(request));

    }




}
