package com.shuyun.apaas.connector.fast.cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.inject.Singleton;
import lombok.NonNull;

import java.time.Duration;
import java.util.*;


@Singleton
public class CacheManager {

    private final Map<String, SyncCache> cacheMap;
    public CacheManager() {
        this.cacheMap = new LinkedHashMap();
        Caffeine<Object, Object> bizCacheBuilder = Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(1000);
        SyncCache bizCache = new SyncCache(bizCacheBuilder.build());
        cacheMap.put("bizCache", bizCache);

        Caffeine<Object, Object> projectCacheBuilder = Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(5000);
        SyncCache benefitProjectCache = new SyncCache(projectCacheBuilder.build());
        cacheMap.put("benefitProjectCache", benefitProjectCache);

        Caffeine<Object, Object> selectorCacheBuilder = Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(200)
                .expireAfterAccess(Duration.ofMinutes(10));
        SyncCache selectorCache = new SyncCache(selectorCacheBuilder.build());
        cacheMap.put("selectorCache", selectorCache);

        Caffeine<Object, Object> gradeMetaCacheBuilder = Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(200)
                .expireAfterAccess(Duration.ofMinutes(10));
        SyncCache gradeMetaCache = new SyncCache(gradeMetaCacheBuilder.build());
        cacheMap.put("gradeMetaCache", gradeMetaCache);

        Caffeine<Object, Object> medalMetaCacheBuilder = Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(200)
                .expireAfterAccess(Duration.ofMinutes(10));
        SyncCache medalMetaCache = new SyncCache(medalMetaCacheBuilder.build());
        cacheMap.put("medalMetaCache", medalMetaCache);
    }


    @NonNull
    public Set<String> getCacheNames() {
        return this.cacheMap.keySet();
    }

    @NonNull
    public SyncCache getCache(String name) {
        SyncCache cache = (SyncCache)this.cacheMap.get(name);
        return cache;
    }
}
