package com.shuyun.apaas.connector.fast.util;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class MessageSourceUtils {

    private final static Map<String, String> messageMap = new ConcurrentHashMap<>();

    static {
        Locale locale = new Locale("en", "US");
        // 加载资源文件
        ResourceBundle bundle = ResourceBundle.getBundle("i18n.api.messages", locale);
        Enumeration<String> keys = bundle.getKeys();
        Iterator<String> iterator = keys.asIterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            messageMap.put(key, bundle.getString(key));
        }
    }

    public static String getMessage(String key, Object... variables) {
        return String.format(messageMap.get(key), variables);
    }

    public static void main(String[] args) {
        System.out.println(getMessage("500000", "test"));
    }
}
