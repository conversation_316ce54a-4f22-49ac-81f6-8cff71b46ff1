package com.shuyun.apaas.connector.fast.handler.v1_0_0.coupon;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.BenefitService;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.param.coupon.CouponGrantRepealParam;
import com.shuyun.ticket.base.resource.dto.TicketIdentity;
import com.shuyun.ticket.benefit.vo.request.benefit.BenefitDiscardRequest;
import com.shuyun.ticket.benefit.vo.response.benefit.BenefitDiscardResponse;
import com.shuyun.ticket.enums.TransactionStatus;
import com.shuyun.ticket.util.JsonUtil;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class CouponGrantRepealApiHandler extends AbstractApiHandler<CouponGrantRepealParam, List<Coupon>, BenefitDiscardRequest, BenefitDiscardResponse> {
    private final BenefitService benefitService;

    @Inject
    public CouponGrantRepealApiHandler(BenefitService benefitService){
        this.benefitService = benefitService;
    }

    @Override
    public void validate(CouponGrantRepealParam param) {
        super.validate(param);
    }

    @Override
    public CouponGrantRepealParam beforeRequest(CouponGrantRepealParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public BenefitDiscardRequest prepareParam(CouponGrantRepealParam param) {
        BenefitDiscardRequest request = benefitService.setSceneParam(new BenefitDiscardRequest(), param);
        request.setIds(param.getCoupons()
                .stream()
                .map(c->new TicketIdentity(c.getId(), c.getCode(), c.getProjectId()))
                .collect(Collectors.toList()));
        return request;
    }

    @Override
    public BenefitDiscardResponse request(BenefitDiscardRequest invokeParam) {
        return benefitService.grantRepeal(invokeParam);
    }

    @Override
    public List<Coupon> prepareResult(CouponGrantRepealParam param, BenefitDiscardResponse result) {

        if(TransactionStatus.SUCCESS.getCode().equals(result.getStatus().getCode())){
            List<Coupon> coupons = result.getSuccess()
                    .stream()
                    .map(c->{
                        Coupon coupon = JsonUtil.copyValue(c, new Coupon());
                        return coupon;
                    })
                    .collect(Collectors.toList());
            if(coupons.size() < param.getCoupons().size()){
                log.warn("return codes number:{} less than request number:{}", coupons.size(), param.getCoupons().size());
            }
            return coupons;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500235, result.getMessage());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_COUPON_GRANT_REPEAL;
    }
}
