package com.shuyun.apaas.connector.fast.operation;
import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.domain.Coupon;
import com.shuyun.fast.v1_0_0.domain.CouponProject;
import com.shuyun.fast.v1_0_0.param.coupon.*;
import com.shuyun.fast.v1_0_0.result.CouponDiscountCalcResult;
import com.shuyun.fast.v1_0_0.result.CouponGetResult;
import com.shuyun.ticket.benefit.vo.Order;
import com.shuyun.ticket.benefit.vo.Page;
import jakarta.inject.Inject;
import java.util.List;

@Title("卡券场景")
public class CouponOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("卡券发放")
    @Api(name = ApiTags.API_NAME_COUPON_GRANT)
    public ApiResult<List<Coupon>> grant(@Parameter CouponGrantParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券作废")
    @Api(name = ApiTags.API_NAME_COUPON_GRANT_REPEAL)
    public ApiResult<List<Coupon>> grantRepeal(@Parameter CouponGrantRepealParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("优惠券列表")
    @Api(name = ApiTags.API_NAME_COUPON_LIST)
    public ApiResult<Page<CouponGetResult>> list(@Parameter CouponListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("可用券列表")
    @Api(name = ApiTags.API_NAME_COUPON_AVAILABLE_LIST)
    public ApiResult<List<CouponGetResult>> availableList(@Parameter CouponAvailableListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("券优惠计算")
    @Api(name = ApiTags.API_NAME_COUPON_DISCOUNT_CALC)
    public ApiResult<CouponDiscountCalcResult> discountCalc(@Parameter CouponDiscountCalcParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券锁定")
    @Api(name = ApiTags.API_NAME_COUPON_LOCK)
    public ApiResult<List<Coupon>> lock(@Parameter CouponLockParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券解锁")
    @Api(name = ApiTags.API_NAME_COUPON_UNLOCK)
    public ApiResult<List<Coupon>> unlock(@Parameter CouponUnlockParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("券详情查询")
    @Api(name = ApiTags.API_NAME_COUPON_GET)
    public ApiResult<CouponGetResult> get(@Parameter CouponGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("券项目列表查询")
    @Api(name = ApiTags.API_NAME_COUPON_PROJECT_LIST)
    public ApiResult<Page<CouponProject>> projectList(@Parameter CouponProjectListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("券项目查询")
    @Api(name = ApiTags.API_NAME_COUPON_PROJECT_GET)
    public ApiResult<CouponProject> projectGet(@Parameter CouponProjectGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券核销")
    @Api(name = ApiTags.API_NAME_COUPON_CONSUME)
    public ApiResult<Order> consume(@Parameter CouponConsumeParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券反核销")
    @Api(name = ApiTags.API_NAME_COUPON_CONSUME_REPEAL)
    public ApiResult<Void> consumeRepeal(@Parameter CouponConsumeRepealParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券转赠")
    @Api(name = ApiTags.API_NAME_COUPON_TRANSFER)
    public ApiResult<Void> transfer(@Parameter CouponTransferParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券受赠")
    @Api(name = ApiTags.API_NAME_COUPON_RECEIVE)
    public ApiResult<Void> receive(@Parameter CouponReceiveParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("卡券退回")
    @Api(name = ApiTags.API_NAME_COUPON_RETURN)
    public ApiResult<Void> returns(@Parameter CouponReturnParam param)  {
        return handlerRouter.route(param).handle(param);
    }
}
