package com.shuyun.apaas.connector.fast.model.http.param.tag;

import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Introspected
public class TagTaggingParam extends ApiBaseParam {
    @Schema(title = "操作人", required = true, maxLength = 32)
    @NotBlank
    private String operator;
    @Schema(title = "标签id")
    private String tagId;
    @Schema(title = "标签值(如果tagId对应的标签不是无值标签,则必输)")
    private List<String> tagValues;
    @Schema(hidden = true, title = "是否批量打标,默认为false 为true时memberIds集合不能为空", defaultValue = "false")
    private Boolean batchTagging = false;
    @Schema(title = "批量打标时会员id集合:与tags集合不能同时有值")
    private List<String> memberIds;
    @Schema(title = "单会员多个标签打标场景:与memberIds集合不能同时有值")
    private List<CustomerTagsOperateRequest.OperateTag> tags;
    @Schema(title = "单会员打标时会员识别对象(memberId与userId二者仅可选其一)")
    private MemberIdentifyParam identify;


    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_TAGGING;
    }
}
