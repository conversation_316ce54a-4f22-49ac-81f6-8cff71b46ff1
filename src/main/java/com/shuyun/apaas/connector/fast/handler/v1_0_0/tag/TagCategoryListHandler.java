package com.shuyun.apaas.connector.fast.handler.v1_0_0.tag;

import com.shuyun.cdp.tags.response.openapi.CategoryTreeResponse;
import com.shuyun.cdp.tags.vo.category.OpenCategoryVo;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.TagService;
import com.shuyun.fast.v1_0_0.param.tag.TagCategoryListParam;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Singleton
public class TagCategoryListHandler extends AbstractApiHandler<TagCategoryListParam, List<OpenCategoryVo>, TagCategoryListParam, CategoryTreeResponse> {
    private final TagService tagService;

    @Inject
    public TagCategoryListHandler(TagService tagService){
        this.tagService = tagService;
    }

    @Override
    public TagCategoryListParam beforeRequest(TagCategoryListParam param) {
        super.beforeRequest(param);
        return param;
    }

    @Override
    public void validate(TagCategoryListParam param) {
        super.validate(param);
    }

    @Override
    public TagCategoryListParam prepareParam(TagCategoryListParam param) {
        return param;
    }

    @Override
    public CategoryTreeResponse request(TagCategoryListParam invokeParam) {
        return tagService.categoryList(invokeParam);
    }

    @Override
    public List<OpenCategoryVo> prepareResult(TagCategoryListParam param, CategoryTreeResponse result) {
        if(ApiTags.API_RESP_SUCCESS.equals(result.getResponseCode())){
            return result.getData();
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500400, result.getResponseMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_TAG_CATEGORY_LIST;
    }
}
