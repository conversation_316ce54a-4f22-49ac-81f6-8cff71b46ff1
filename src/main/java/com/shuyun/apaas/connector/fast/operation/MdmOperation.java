package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.v1_0_0.domain.MdmShop;
import com.shuyun.fast.v1_0_0.param.mdm.MdmOrgSyncParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmProductSyncParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopListParam;
import com.shuyun.fast.v1_0_0.param.mdm.MdmShopSyncParam;
import jakarta.inject.Inject;

@Title("mdm场景")
public class MdmOperation extends DispatcherOperation {
    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("商品同步")
    @Api(name = ApiTags.API_NAME_MDM_PRODUCT_SYNC)
    public ApiResult<Void> productSync(@Parameter MdmProductSyncParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("门店同步")
    @Api(name = ApiTags.API_NAME_MDM_SHOP_SYNC)
    public ApiResult<Void> shopSync(@Parameter MdmShopSyncParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("门店列表")
    @Api(name = ApiTags.API_NAME_MDM_SHOP_LIST)
    public ApiResult<PageResult<MdmShop>> shopList(@Parameter MdmShopListParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("组织结构同步")
    @Api(name = ApiTags.API_NAME_MDM_ORG_SYNC)
    public ApiResult<Void> orgSync(@Parameter MdmOrgSyncParam param)  {
        return handlerRouter.route(param).handle(param);
    }

}
