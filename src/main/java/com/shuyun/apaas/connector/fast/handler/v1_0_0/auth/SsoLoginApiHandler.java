package com.shuyun.apaas.connector.fast.handler.v1_0_0.auth;

import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.model.http.param.auth.SsoLoginParam;
import com.shuyun.apaas.connector.fast.model.http.result.auth.SsoLoginResult;
import com.shuyun.apaas.connector.fast.service.v1_0_0.AuthService;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

/**
 * 单点登录处理器
 * 参考门店同步接口实现
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Singleton
public class SsoLoginApiHandler extends AbstractApiHandler<SsoLoginParam, SsoLoginResult,SsoLoginParam,SsoLoginResult> {

    private final AuthService authService;

    @Inject
    public SsoLoginApiHandler(AuthService authService) {
        this.authService = authService;
    }

    @Override
    public void validate(SsoLoginParam param) {
        super.validate(param);
        
        // 额外的token格式验证
        if (param.getToken().length() < 10) {
            throw new IllegalArgumentException("访问令牌格式不正确");
        }
    }

    @Override
    public SsoLoginParam beforeRequest(SsoLoginParam param) {
        super.beforeRequest(param);
        
        // 处理token格式，如果需要添加Bearer前缀
        String token = param.getToken();
        if (!token.startsWith("Bearer ") && !token.startsWith("bearer ")) {
            // 根据实际需要决定是否添加Bearer前缀
            // param.setToken("Bearer " + token);
        }
        
        return param;
    }

    @Override
    public SsoLoginParam prepareParam(SsoLoginParam param) {
        // 可以在这里进行参数预处理，如时区转换等
        return param;
    }

    @Override
    public SsoLoginResult request(SsoLoginParam invokeParam) {
        return authService.ssoLogin(invokeParam);
    }

    @Override
    public SsoLoginResult prepareResult(SsoLoginParam param, SsoLoginResult result) {
        // 可以在这里对结果进行后处理
        return result;
    }

    @Override
    public String apiName() {
        return "auth.sso.login";
    }
}
