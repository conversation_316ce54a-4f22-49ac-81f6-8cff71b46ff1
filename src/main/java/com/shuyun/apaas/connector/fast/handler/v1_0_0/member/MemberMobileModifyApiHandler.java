package com.shuyun.apaas.connector.fast.handler.v1_0_0.member;

import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.apaas.connector.fast.handler.api.AbstractApiHandler;
import com.shuyun.apaas.connector.fast.service.v1_0_0.MemberService;
import com.shuyun.fast.v1_0_0.domain.MemberId;
import com.shuyun.fast.v1_0_0.param.member.MemberIdentifyParam;
import com.shuyun.fast.v1_0_0.param.member.MemberMobileModifyParam;
import com.shuyun.apaas.connector.fast.validator.FastValidator;
import com.shuyun.kylin.member.api.response.MemberBaseResponse;
import com.shuyun.kylin.member.api.response.MemberBaseResult;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
@Singleton
public class MemberMobileModifyApiHandler extends AbstractApiHandler<MemberMobileModifyParam, MemberId, MemberMobileModifyParam, MemberBaseResult> {

    private final MemberService memberService;

    @Inject
    public MemberMobileModifyApiHandler(MemberService memberService) {
        this.memberService = memberService;
    }

    @Override
    public void validate(MemberMobileModifyParam param) {
        super.validate(param);
        MemberIdentifyParam identify = param.getIdentify();
        String memberId = identify.getMemberId();
        String userId = identify.getUserId();
        FastValidator.eitherOrValidate(memberId, userId, "memberId","userId");
    }

    @Override
    public MemberMobileModifyParam beforeRequest(MemberMobileModifyParam param) {
        super.beforeRequest(param);
        param.setIdentify(memberService.memberIdentify(param.getIdentify(), param));
        return param;
    }

    @Override
    public MemberMobileModifyParam prepareParam(MemberMobileModifyParam param) {
        return param;
    }

    @Override
    public MemberBaseResult request(MemberMobileModifyParam invokeParam) {
        return memberService.mobileModify(invokeParam);
    }

    @Override
    public MemberId prepareResult(MemberMobileModifyParam param, MemberBaseResult result) {
        if(StringUtils.isEmpty(result.getError_code()) && Objects.nonNull(result.getData())){
            MemberBaseResponse response = result.getData();
            MemberId memberId = new MemberId();
            memberId.setMemberId(response.getMemberId());
            return memberId;
        }
        throw new ApiException(ApiTags.API_RESP_CODE_500206, result.getMsg());
    }

    @Override
    public String apiName() {
        return ApiTags.API_NAME_MEMBER_MOBILE_MODIFY;
    }
}
