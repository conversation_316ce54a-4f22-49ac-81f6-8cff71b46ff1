package com.shuyun.apaas.connector.fast;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.apaas.cnc.api.Connector;
import com.shuyun.apaas.cnc.api.annotations.Configuration;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.ConnectorManager;
import com.shuyun.apaas.connector.fast.ser.LocalDateTimeSerializer;

import java.time.LocalDateTime;

@Title("fast-connector-加多宝")
@Configuration(category = "custom", organization = "xian", lazy = false, tenant = "jdb")
public class FastConnector implements Connector {
    public FastConnector(){
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
        mapper.registerModule(new JavaTimeModule());
        mapper.registerModule(module);
        ConnectorManager.INSTANCE.setMapper(mapper);
    }
}
