package com.shuyun.apaas.connector.fast.operation;

import com.shuyun.apaas.cnc.api.annotations.Operation;
import com.shuyun.apaas.cnc.api.annotations.Parameter;
import com.shuyun.apaas.cnc.api.annotations.Title;
import com.shuyun.apaas.cnc.core.DispatcherOperation;
import com.shuyun.apaas.connector.fast.annotation.Api;
import com.shuyun.apaas.connector.fast.router.ApiHandlerRouter;
import com.shuyun.fast.base.ApiResult;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.fast.base.PageResult;
import com.shuyun.fast.v1_0_0.domain.TradeMainOrder;
import com.shuyun.fast.v1_0_0.param.trade.TradeGetParam;
import com.shuyun.fast.v1_0_1.param.trade.TradeOrderSyncParam;
import com.shuyun.fast.v1_0_1.param.trade.TradeRefundSyncParam;
import jakarta.inject.Inject;

@Title("订单场景")
public class TradeOperation extends DispatcherOperation {

    @Inject
    private ApiHandlerRouter handlerRouter;

    @Operation("订单同步")
    @Api(name = ApiTags.API_NAME_TRADE_ORDER_SYNC)
    public ApiResult<Void> orderSync(@Parameter TradeOrderSyncParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("退单同步")
    @Api(name = ApiTags.API_NAME_TRADE_REFUND_SYNC)
    public ApiResult<Void> refundSync(@Parameter TradeRefundSyncParam param)  {
        return handlerRouter.route(param).handle(param);
    }

    @Operation("订单查询")
    @Api(name = ApiTags.API_NAME_TRADE_GET)
    public ApiResult<PageResult<TradeMainOrder>> get(@Parameter TradeGetParam param)  {
        return handlerRouter.route(param).handle(param);
    }

}
