package com.shuyun.apaas.connector.fast.service.v1_0_0;

import com.shuyun.apaas.connector.fast.client.CdpClient;
import com.shuyun.apaas.connector.fast.client.manager.ClientManager;
import com.shuyun.apaas.connector.fast.config.FastConfig;
import com.shuyun.apaas.connector.fast.model.BizCache;
import com.shuyun.cdp.common.response.BaseResponse;
import com.shuyun.cdp.tags.enums.OperateTagType;
import com.shuyun.cdp.tags.request.openapi.CustomerTagQueryRequest;
import com.shuyun.cdp.tags.request.openapi.CustomerTagsOperateRequest;
import com.shuyun.cdp.tags.request.openapi.CustomersTagOperateRequest;
import com.shuyun.cdp.tags.response.openapi.CategoryTreeResponse;
import com.shuyun.cdp.tags.response.openapi.CustomerTagQueryResponse;
import com.shuyun.cdp.tags.response.openapi.TagListResponse;
import com.shuyun.fast.base.ApiBaseParam;
import com.shuyun.fast.base.ApiTags;
import com.shuyun.apaas.connector.fast.cache.v1_0_0.TagCache;
import com.shuyun.apaas.connector.fast.exception.ApiException;
import com.shuyun.fast.v1_0_0.param.tag.*;
import com.shuyun.apaas.connector.fast.model.http.param.tag.TagTaggingParam;
import com.shuyun.apaas.connector.fast.model.http.param.tag.TagUntaggingParam;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Singleton
@Slf4j
public class TagService {
    private final CdpClient cdpClient;
    private final BizCacheService bizCacheService;
    private final String tenantId;

    @Inject
    public TagService(ClientManager clientManager,
                      BizCacheService bizCacheService,
                      FastConfig config){
        tenantId = config.getEnv();
        this.cdpClient = clientManager.getCdpClient();
        this.bizCacheService = bizCacheService;
    }

    public TagCache bizCacheGet(ApiBaseParam param){
        TagCache cache = bizCacheService.get(TagCache.class, tenantId, param.getBizCode(), BizCache.TAG)
                .stream()
                .findFirst()
                .orElse(null);
        if(Objects.isNull(cache)){
            throw new ApiException(ApiTags.API_RESP_CODE_500001, BizCache.TAG);
        }
        return cache;
    }

    public CategoryTreeResponse categoryList(TagCategoryListParam param){
        TagCache cache = bizCacheGet(param);
        return cdpClient.getCategory(cache.getDomainCode(), param.getCategoryId());
    }

    public TagListResponse list(TagListParam param){
        TagCache cache = bizCacheGet(param);
        return cdpClient.getTags(cache.getDomainCode(),
                param.getCategoryIds(),
                param.getPage(),
                param.getPageSize());
    }

    public BaseResponse<String> tagging(TagTaggingParam param){
        TagCache cache = bizCacheGet(param);
        if(CollectionUtils.isEmpty(param.getTags())){
            CustomersTagOperateRequest request = new CustomersTagOperateRequest();
            request.setOrigin(param.getRequestChannel());
            request.setChannelType(cache.getChannelType());
            request.setOperator(param.getOperator());
            request.setOriginIds(param.getBatchTagging()?param.getMemberIds():Collections.singletonList(param.getIdentify().getMemberId()));
            request.setMark(true);
            request.setTagId(param.getTagId());
            request.setTagValues(new ArrayList<>(param.getTagValues()));
            request.setNoValueTagSync(CollectionUtils.isEmpty(param.getTagValues()));
            request.setOperateTagType(OperateTagType.REPLACE);
            request.setOperateWithBind(false);
            return cdpClient.operateCustomersTag(cache.getDomainCode(), request);
        }else{
            CustomerTagsOperateRequest request = new CustomerTagsOperateRequest();
            request.setChannelType(cache.getChannelType());
            request.setOperator(param.getOperator());
            request.setOriginId(param.getIdentify().getMemberId());
            List<CustomerTagsOperateRequest.OperateTag> tags = param.getTags();
            tags.forEach(t->{
                t.setMark(true);
                t.setOperateTagType(OperateTagType.REPLACE);
            });
            request.setOperateTags(tags);
            request.setOperateWithBind(false);
            return cdpClient.operateCustomerTags(cache.getDomainCode(), request);
        }

    }

    public BaseResponse<String> untagging(TagUntaggingParam param){
        TagCache cache = bizCacheGet(param);
        if(CollectionUtils.isEmpty(param.getTags())){
            CustomersTagOperateRequest request = new CustomersTagOperateRequest();
            request.setOrigin(param.getRequestChannel());
            request.setChannelType(cache.getChannelType());
            request.setOperator(param.getOperator());
            request.setOriginIds(param.getBatchUntagging()?param.getMemberIds():Collections.singletonList(param.getIdentify().getMemberId()));
            request.setMark(false);
            request.setTagId(param.getTagId());
            request.setTagValues(new ArrayList<>(param.getTagValues()));
            request.setNoValueTagSync(CollectionUtils.isEmpty(param.getTagValues()));
            request.setOperateTagType(OperateTagType.CLEAR);
            request.setOperateWithBind(false);
            return cdpClient.operateCustomersTag(cache.getDomainCode(), request);
        }else{
            CustomerTagsOperateRequest request = new CustomerTagsOperateRequest();
            request.setChannelType(cache.getChannelType());
            request.setOperator(param.getOperator());
            request.setOriginId(param.getIdentify().getMemberId());
            List<CustomerTagsOperateRequest.OperateTag> tags = param.getTags();
            tags.forEach(t->{
                t.setMark(false);
                t.setOperateTagType(OperateTagType.CLEAR);
            });
            request.setOperateTags(tags);
            request.setOperateWithBind(false);
            return cdpClient.operateCustomerTags(cache.getDomainCode(), request);
        }

    }

    public CustomerTagQueryResponse userTags(TagUserTagsParam param){
        TagCache cache = bizCacheGet(param);
        CustomerTagQueryRequest request = new CustomerTagQueryRequest();
        request.setChannelType(cache.getChannelType());
        request.setTagIds(param.getTagIds());
        request.setOriginIds(Collections.singleton(param.getIdentify().getMemberId()));
        request.setIsUsedBinding(false);
        return cdpClient.getTagsByCustomer(cache.getDomainCode(), request);
    }

}
