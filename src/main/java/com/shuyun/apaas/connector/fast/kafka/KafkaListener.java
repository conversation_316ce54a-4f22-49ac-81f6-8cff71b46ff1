package com.shuyun.apaas.connector.fast.kafka;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.shuyun.apaas.connector.fast.config.KafkaConfig;
import com.shuyun.lite.util.Common;
import io.micronaut.core.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import java.time.Duration;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;

@Slf4j
public class KafkaListener {

    private final static ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.registerModule(new JavaTimeModule());
    }

    public <T> KafkaListener(List<String> topics, KafkaConfig config, Boolean uniqueGroupId, T event, BiConsumer<String, T> biConsumer){

        final ExecutorService executorService = Executors.newFixedThreadPool(1);
        Properties props = new Properties();
        String address = config.getAddress();
        if(StringUtils.isEmpty(address)){
            address = Common.getSysOrEnv("kafka.address");
        }
        props.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, address);
        String groupId = config.getGroupId();
        if(uniqueGroupId){
            groupId = groupId + "_" + UUID.randomUUID();
        }
        props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        props.setProperty(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");//default value
        props.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.setProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, config.getMaxPollRecords());
        props.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, config.getOffsetReset());
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(topics);

        executorService.execute(()->{
            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration. ofMillis(100));
                for (ConsumerRecord<String, String> record : records){
                    try {
                        log.debug("offset = {}, key = {}, value = {}", record.offset(), record.key(), record.value());
                        T value = (T)objectMapper.readValue(record.value(), event.getClass());
                        biConsumer.accept(record.key(), value);
                        consumer.commitSync();// manual ack
                    } catch (Exception e){
                        log.error("kafka message deserialize error", e);
                    }
                }
            }
        });
    }
}
