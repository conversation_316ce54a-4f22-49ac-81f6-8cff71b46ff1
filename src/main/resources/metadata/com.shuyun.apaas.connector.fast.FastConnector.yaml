---
name: "fast-connector-加多宝"
category: "custom"
organization: "xian"
version: "1.0.1-SNAPSHOT"
desc: ""
tenant: "jdb"
type: "CODE"
metadata:
  plugin: "com.shuyun.apaas.connector.fast.FastConnector"
  groupId: "com.shuyun.fast"
  artifactId: "fast-connector-jdb"
  version: "1.0.1-SNAPSHOT"
  versionId: 0
content:
  config:
    title: "fast连接器配置属性"
    name: "com.shuyun.apaas.connector.fast.config.FastConfig"
    schema:
      $schema: "https://json-schema.org/draft/2020-12/schema"
      type: "object"
      properties:
        kylinRequestLogEnable:
          type: "boolean"
          title: "麒麟内部请求是否记录日志:默认关闭"
          default: "false"
        requestUrl:
          type: "string"
          title: "请求的kylin url的地址(用http://开头)"
          maxLength: 200
        callerKey:
          type: "string"
          title: "请求的签名key"
        callerSecret:
          type: "string"
          title: "请求的签名秘钥"
        clientId:
          type: "string"
          title: "请求的kylin客户端id"
        clientSecret:
          type: "string"
          title: "请求的kylin客户端密钥"
        env:
          type: "string"
          title: "请求的kylin环境"
        ebrandEnable:
          type: "boolean"
          title: "是否开启会员通:默认开启(不需要的项目报错后再关闭)"
          default: "true"
        clientDiscoveryEnable:
          type: "boolean"
          title: "是否开启麒麟客户端服务发现:默认开启"
          default: "false"
        dataapi:
          type: "object"
          properties:
            dataApiServerUrl:
              type: "string"
              title: "数据服务url"
          required:
          - "dataApiServerUrl"
          title: "dataapi配置"
          x-ui-type: "GROUP"
        eventService:
          type: "object"
          properties:
            serverUrl:
              type: "string"
              title: "事件服务url"
            caller:
              type: "string"
              title: "调用事件服务caller"
            secret:
              type: "string"
              title: "调用事件服务secret"
            userName:
              type: "string"
              title: "事件服务userName"
            userSecret:
              type: "string"
              title: "调用事件服务userSecret"
          required:
          - "serverUrl"
          - "caller"
          - "secret"
          - "userName"
          - "userSecret"
          title: "eventService配置"
          x-ui-type: "GROUP"
        benefit:
          type: "object"
          properties:
            enable:
              type: "boolean"
              title: "是否开启卡券模块:默认不开启"
          title: "benefit配置"
          x-ui-type: "GROUP"
        redis:
          type: "object"
          properties:
            enable:
              type: "boolean"
              title: "是否启动redis:默认开启"
              default: "true"
            ssl:
              type: "string"
              title: "是否开启ssl:默认false"
              default: "false"
            address:
              type: "string"
              title: "address:默认从配置中心读取(可替换)"
            password:
              type: "string"
              title: "password:默认从配置中心读取(可替换)"
            poolSize:
              type: "string"
              title: "poolSize:默认值64"
              default: "64"
            database:
              type: "string"
              title: "database:默认值0"
              default: "0"
            nettyThread:
              type: "integer"
              format: "int32"
              title: "nettyThread:默认值64"
              default: "64"
          title: "redis配置"
          x-ui-type: "GROUP"
        kafka:
          type: "object"
          properties:
            address:
              type: "string"
              title: "kafka broker地址:默认从配置中心读取,可单独配置"
            groupId:
              type: "string"
              title: "kafka消费组Id(默认值:fast-connector)"
              default: "fast-connector"
            offsetReset:
              type: "string"
              title: "kafka offset reset策略(默认值:latest)"
              default: "latest"
            maxPollRecords:
              type: "string"
              title: "max.poll.records 每次最多拉取多少条记录(默认值:10)"
              default: "10"
          title: "kafka配置"
          x-ui-type: "GROUP"
      required:
      - "requestUrl"
      - "callerKey"
      - "callerSecret"
      - "clientId"
      - "clientSecret"
      - "env"
    render:
      type: "object"
      properties:
      - type: "boolean"
        title: "麒麟内部请求是否记录日志:默认关闭"
        default: "false"
        key: "kylinRequestLogEnable"
      - type: "string"
        title: "请求的kylin url的地址(用http://开头)"
        maxLength: 200
        key: "requestUrl"
      - type: "string"
        title: "请求的签名key"
        key: "callerKey"
      - type: "string"
        title: "请求的签名秘钥"
        key: "callerSecret"
      - type: "string"
        title: "请求的kylin客户端id"
        key: "clientId"
      - type: "string"
        title: "请求的kylin客户端密钥"
        key: "clientSecret"
      - type: "string"
        title: "请求的kylin环境"
        key: "env"
      - type: "boolean"
        title: "是否开启会员通:默认开启(不需要的项目报错后再关闭)"
        default: "true"
        key: "ebrandEnable"
      - type: "boolean"
        title: "是否开启麒麟客户端服务发现:默认开启"
        default: "false"
        key: "clientDiscoveryEnable"
      - type: "object"
        properties:
        - type: "string"
          title: "数据服务url"
          key: "dataApiServerUrl"
        required:
        - "dataApiServerUrl"
        title: "dataapi配置"
        x-ui-type: "GROUP"
        key: "dataapi"
      - type: "object"
        properties:
        - type: "string"
          title: "事件服务url"
          key: "serverUrl"
        - type: "string"
          title: "调用事件服务caller"
          key: "caller"
        - type: "string"
          title: "调用事件服务secret"
          key: "secret"
        - type: "string"
          title: "事件服务userName"
          key: "userName"
        - type: "string"
          title: "调用事件服务userSecret"
          key: "userSecret"
        required:
        - "serverUrl"
        - "caller"
        - "secret"
        - "userName"
        - "userSecret"
        title: "eventService配置"
        x-ui-type: "GROUP"
        key: "eventService"
      - type: "object"
        properties:
        - type: "boolean"
          title: "是否开启卡券模块:默认不开启"
          key: "enable"
        title: "benefit配置"
        x-ui-type: "GROUP"
        key: "benefit"
      - type: "object"
        properties:
        - type: "boolean"
          title: "是否启动redis:默认开启"
          default: "true"
          key: "enable"
        - type: "string"
          title: "是否开启ssl:默认false"
          default: "false"
          key: "ssl"
        - type: "string"
          title: "address:默认从配置中心读取(可替换)"
          key: "address"
        - type: "string"
          title: "password:默认从配置中心读取(可替换)"
          key: "password"
        - type: "string"
          title: "poolSize:默认值64"
          default: "64"
          key: "poolSize"
        - type: "string"
          title: "database:默认值0"
          default: "0"
          key: "database"
        - type: "integer"
          format: "int32"
          title: "nettyThread:默认值64"
          default: "64"
          key: "nettyThread"
        title: "redis配置"
        x-ui-type: "GROUP"
        key: "redis"
      - type: "object"
        properties:
        - type: "string"
          title: "kafka broker地址:默认从配置中心读取,可单独配置"
          key: "address"
        - type: "string"
          title: "kafka消费组Id(默认值:fast-connector)"
          default: "fast-connector"
          key: "groupId"
        - type: "string"
          title: "kafka offset reset策略(默认值:latest)"
          default: "latest"
          key: "offsetReset"
        - type: "string"
          title: "max.poll.records 每次最多拉取多少条记录(默认值:10)"
          default: "10"
          key: "maxPollRecords"
        title: "kafka配置"
        x-ui-type: "GROUP"
        key: "kafka"
      required:
      - "requestUrl"
      - "callerKey"
      - "callerSecret"
      - "clientId"
      - "clientSecret"
      - "env"
  exception:
    schema:
      $schema: "https://json-schema.org/draft/2020-12/schema"
      type: "object"
      properties:
        code:
          type: "string"
          title: "错误码"
        message:
          type: "string"
          title: "错误消息"
        type:
          type: "string"
          enum:
          - "Client"
          - "Server"
          - "Remote"
          - "Unknown"
          title: "错误类型"
        module:
          type: "string"
          title: "模块名称"
      required:
      - "code"
      - "message"
      title: "错误码定义"
operations:
- method: "com.shuyun.apaas.connector.fast.operation.ActivityOperation.prizeRecordSync"
  summary: "活动领奖记录同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          id:
            type: "string"
          actId:
            type: "string"
          actName:
            type: "string"
          productName:
            type: "string"
          userId:
            type: "string"
          nickname:
            type: "string"
          phone:
            type: "string"
          inviteTime:
            type: "string"
            format: "date-time"
          inviterId:
            type: "string"
          inviterName:
            type: "string"
          inviterPhone:
            type: "string"
          inviterChannelId:
            type: "string"
          inviterChannelName:
            type: "string"
          gpsLocation:
            type: "string"
          provinceName:
            type: "string"
          cityName:
            type: "string"
          areaName:
            type: "string"
          memberType:
            type: "string"
          channelType:
            type: "string"
          isValid:
            type: "string"
          lastSync:
            type: "string"
            format: "date-time"
          extension:
            type: "object"
      name: "ActivityPrizeRecordSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.availableList"
  summary: "可用券列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          showProject:
            type: "boolean"
          checkByOrderItem:
            type: "boolean"
          order:
            type: "object"
            properties:
              extension:
                type: "object"
              orderId:
                type: "string"
              amount:
                type: "number"
              paymentAmount:
                type: "number"
              discountAmount:
                type: "number"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    extension:
                      type: "object"
                    orderItemId:
                      type: "string"
                    price:
                      type: "number"
                    specialPrice:
                      type: "number"
                    goodsId:
                      type: "string"
                    num:
                      type: "integer"
                      format: "int32"
                    amount:
                      type: "number"
                    paymentAmount:
                      type: "number"
                    discountAmount:
                      type: "number"
          showSelectorData:
            type: "boolean"
      name: "CouponAvailableListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                includeShops:
                  type: "array"
                  items:
                    type: "string"
                excludeShops:
                  type: "array"
                  items:
                    type: "string"
                includeGoods:
                  type: "array"
                  items:
                    type: "string"
                excludeGoods:
                  type: "array"
                  items:
                    type: "string"
                allIncludeGoods:
                  type: "array"
                  items:
                    type: "string"
                anyExcludeGoods:
                  type: "array"
                  items:
                    type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                programId:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                effectiveAt:
                  type: "string"
                  format: "date-time"
                useAt:
                  type: "string"
                  format: "date-time"
                expiredAt:
                  type: "string"
                  format: "date-time"
                status:
                  type: "string"
                useCountRestrict:
                  type: "integer"
                  format: "int64"
                usedCount:
                  type: "integer"
                  format: "int64"
                ruleText:
                  type: "string"
                couponName:
                  type: "string"
                projectType:
                  type: "string"
                reductAmount:
                  type: "number"
                discountRate:
                  type: "number"
                thresholdValue:
                  type: "number"
                denomination:
                  type: "number"
                grantReason:
                  type: "string"
                grantShop:
                  type: "string"
                extension:
                  type: "object"
                project:
                  type: "object"
                  properties:
                    includeShops:
                      type: "array"
                      items:
                        type: "string"
                    excludeShops:
                      type: "array"
                      items:
                        type: "string"
                    includeGoods:
                      type: "array"
                      items:
                        type: "string"
                    excludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    allIncludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    anyExcludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    id:
                      type: "string"
                    branch:
                      type: "string"
                    title:
                      type: "string"
                    kind:
                      type: "string"
                    campaignTitle:
                      type: "string"
                    description:
                      type: "string"
                    status:
                      type: "string"
                    restrict:
                      type: "object"
                      properties:
                        shopsRefType:
                          type: "string"
                        shopsRef:
                          type: "string"
                        platformsRef:
                          type: "string"
                        goodsRefType:
                          type: "string"
                        goodsRef:
                          type: "string"
                    grantRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                        platformsRef:
                          type: "string"
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        invokeNumByPerDay:
                          type: "integer"
                          format: "int32"
                    useRestrict:
                      type: "object"
                      properties:
                        shopsRefType:
                          type: "string"
                        shopsRef:
                          type: "string"
                        platformsRef:
                          type: "string"
                        goodsRefType:
                          type: "string"
                        goodsRef:
                          type: "string"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        wait:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        timeType:
                          type: "string"
                        timeCycle:
                          type: "object"
                          properties:
                            value:
                              type: "array"
                              items:
                                type: "integer"
                                format: "int32"
                            unit:
                              type: "string"
                            timeRange:
                              type: "string"
                            cycleStartTime:
                              type: "string"
                              format: "time"
                            cycleEndTime:
                              type: "string"
                              format: "time"
                    cancelUseRestrict:
                      type: "object"
                      properties:
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    updateAt:
                      type: "string"
                      format: "date-time"
                    maxQuantity:
                      type: "integer"
                      format: "int64"
                    useCountRestrict:
                      type: "integer"
                      format: "int64"
                    transferable:
                      type: "boolean"
                    type:
                      type: "string"
                    discount:
                      type: "object"
                      properties:
                        mode:
                          type: "string"
                        scope:
                          type: "string"
                        amount:
                          type: "object"
                          properties:
                            type:
                              type: "string"
                            value:
                              type: "number"
                            discountValue:
                              type: "number"
                            unit:
                              type: "string"
                            min:
                              type: "number"
                            max:
                              type: "number"
                            threshold:
                              type: "object"
                              properties:
                                type:
                                  type: "string"
                                value:
                                  type: "number"
                                maxValue:
                                  type: "number"
                                num:
                                  type: "integer"
                                  format: "int32"
                                maxNum:
                                  type: "integer"
                                  format: "int32"
                                fixedSteps:
                                  type: "array"
                                  items:
                                    type: "object"
                                    properties:
                                      value:
                                        type: "number"
                                      threshold:
                                        type: "number"
                                steps:
                                  type: "array"
                                  items:
                                    type: "object"
                                    properties:
                                      value:
                                        type: "number"
                                      threshold:
                                        type: "number"
                                goodsMinValue:
                                  type: "number"
                        minAmount:
                          type: "number"
                        maxAmount:
                          type: "number"
                        maxNum:
                          type: "integer"
                          format: "int32"
                    exchange:
                      type: "object"
                      properties:
                        goods:
                          type: "string"
                        num:
                          type: "integer"
                          format: "int32"
                        maxPerGoods:
                          type: "integer"
                          format: "int32"
                    maxOverlayNum:
                      type: "integer"
                      format: "int32"
                    combinationEnabled:
                      type: "boolean"
                    grantedCount:
                      type: "integer"
                      format: "int64"
                    usedCount:
                      type: "integer"
                      format: "int64"
                    reminderBeforeExpirationEnable:
                      type: "boolean"
                    reminderBeforeExpiration:
                      type: "object"
                      properties:
                        year:
                          type: "integer"
                          format: "int32"
                        quarter:
                          type: "integer"
                          format: "int32"
                        month:
                          type: "integer"
                          format: "int32"
                        week:
                          type: "integer"
                          format: "int32"
                        day:
                          type: "integer"
                          format: "int32"
                        hour:
                          type: "integer"
                          format: "int32"
                        minute:
                          type: "integer"
                          format: "int32"
                        second:
                          type: "integer"
                          format: "int32"
                    effectPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        fixed:
                          type: "string"
                          format: "date-time"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    expiredPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        fixed:
                          type: "string"
                          format: "date-time"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    extData:
                      type: "object"
                    template:
                      type: "string"
                    codeMaker:
                      type: "string"
                    effectRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    expireRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    previous:
                      type: "string"
                    program:
                      type: "string"
                    subOrderShareDiscount:
                      type: "boolean"
                    combinationType:
                      type: "string"
                    storeDirectDistribution:
                      type: "boolean"
                    transferRestrict:
                      type: "object"
                      properties:
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    transferPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    weiMob:
                      type: "object"
                      properties:
                        customerDirectReceive:
                          type: "boolean"
                        activityPublish:
                          type: "boolean"
                        enterpriseAssistant:
                          type: "boolean"
                        merchantPublish:
                          type: "boolean"
                        shoppingPublish:
                          type: "boolean"
                        customerListPublish:
                          type: "boolean"
                        servicePublish:
                          type: "boolean"
                        canShare:
                          type: "boolean"
                        canStoreLaunch:
                          type: "boolean"
                        includeStoreGoods:
                          type: "boolean"
                        isAllUseScene:
                          type: "boolean"
                        shoppingMallSceneList:
                          type: "string"
                        canUseWithOtherDiscount:
                          type: "boolean"
                        shoppingMallDiscount:
                          type: "string"
                    weChatPayCouponStocks:
                      type: "object"
                      properties:
                        extension:
                          type: "object"
                        appId:
                          type: "string"
                        belongMerchant:
                          type: "string"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        naturalPersonLimit:
                          type: "boolean"
                        preventApiAbuse:
                          type: "boolean"
                        merchantName:
                          type: "string"
                        backgroundColor:
                          type: "string"
                        goodsTag:
                          type: "string"
                        limitPay:
                          type: "string"
                        limitCardName:
                          type: "string"
                        bin:
                          type: "string"
                        tradeType:
                          type: "string"
                        noCash:
                          type: "boolean"
                        availableMchids:
                          type: "string"
                    couponCost:
                      type: "object"
                      properties:
                        couponCostType:
                          type: "string"
                        couponCost:
                          type: "number"
                        orgShare:
                          type: "object"
                          properties:
                            headquarters:
                              type: "number"
                            franchisee:
                              type: "number"
                            brand:
                              type: "number"
                            branch:
                              type: "number"
                            dealer:
                              type: "number"
                            shops:
                              type: "number"
                    positivePriceGoods:
                      type: "boolean"
                    payBenefit:
                      type: "object"
                      properties:
                        isPayment:
                          type: "boolean"
                    storeLimitNum:
                      type: "integer"
                      format: "int32"
                    orderDeductList:
                      type: "string"
                    materialImage:
                      type: "object"
                      properties:
                        cardVoucherListImage:
                          type: "string"
                        cardVoucherDetailImage:
                          type: "string"
                        brandLogo:
                          type: "string"
                        goodsImage:
                          type: "string"
                    createNow:
                      type: "boolean"
                    activateNow:
                      type: "boolean"
                    subject:
                      type: "string"
                transferState:
                  type: "string"
                useShop:
                  type: "string"
                useOrderId:
                  type: "string"
                activateAt:
                  type: "string"
                  format: "date-time"
                grantPlatform:
                  type: "string"
                scene:
                  type: "string"
                lastState:
                  type: "string"
                receiver:
                  type: "string"
                transferredCount:
                  type: "integer"
                  format: "int32"
                transferredCountRestrict:
                  type: "integer"
                  format: "int32"
                usableOrderItemIds:
                  type: "array"
                  items:
                    type: "string"
                unavailabilityReason:
                  type: "string"
                memberId:
                  type: "string"
      name: "CouponGetResult>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.consume"
  summary: "卡券核销"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
          shopCode:
            type: "string"
          platformId:
            type: "string"
          order:
            type: "object"
            properties:
              extension:
                type: "object"
              orderId:
                type: "string"
              amount:
                type: "number"
              paymentAmount:
                type: "number"
              discountAmount:
                type: "number"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    extension:
                      type: "object"
                    orderItemId:
                      type: "string"
                    price:
                      type: "number"
                    specialPrice:
                      type: "number"
                    goodsId:
                      type: "string"
                    num:
                      type: "integer"
                      format: "int32"
                    amount:
                      type: "number"
                    paymentAmount:
                      type: "number"
                    discountAmount:
                      type: "number"
          lockTransactionId:
            type: "string"
          isCheck:
            type: "boolean"
      name: "CouponConsumeParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              extension:
                type: "object"
              orderId:
                type: "string"
              amount:
                type: "number"
              paymentAmount:
                type: "number"
              discountAmount:
                type: "number"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    extension:
                      type: "object"
                    orderItemId:
                      type: "string"
                    price:
                      type: "number"
                    specialPrice:
                      type: "number"
                    goodsId:
                      type: "string"
                    num:
                      type: "integer"
                      format: "int32"
                    amount:
                      type: "number"
                    paymentAmount:
                      type: "number"
                    discountAmount:
                      type: "number"
      name: "Order>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.consumeRepeal"
  summary: "卡券反核销"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
          useTransactionId:
            type: "string"
      name: "CouponConsumeRepealParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.discountCalc"
  summary: "券优惠计算"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
          shopCode:
            type: "string"
          platformId:
            type: "string"
          order:
            type: "object"
            properties:
              extension:
                type: "object"
              orderId:
                type: "string"
              amount:
                type: "number"
              paymentAmount:
                type: "number"
              discountAmount:
                type: "number"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    extension:
                      type: "object"
                    orderItemId:
                      type: "string"
                    price:
                      type: "number"
                    specialPrice:
                      type: "number"
                    goodsId:
                      type: "string"
                    num:
                      type: "integer"
                      format: "int32"
                    amount:
                      type: "number"
                    paymentAmount:
                      type: "number"
                    discountAmount:
                      type: "number"
          lockTransactionId:
            type: "string"
      name: "CouponDiscountCalcParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              order:
                type: "object"
                properties:
                  extension:
                    type: "object"
                  orderId:
                    type: "string"
                  amount:
                    type: "number"
                  paymentAmount:
                    type: "number"
                  discountAmount:
                    type: "number"
                  items:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        extension:
                          type: "object"
                        orderItemId:
                          type: "string"
                        price:
                          type: "number"
                        specialPrice:
                          type: "number"
                        goodsId:
                          type: "string"
                        num:
                          type: "integer"
                          format: "int32"
                        amount:
                          type: "number"
                        paymentAmount:
                          type: "number"
                        discountAmount:
                          type: "number"
              discountResults:
                type: "array"
                items:
                  type: "object"
                  properties:
                    identity:
                      type: "object"
                      properties:
                        id:
                          type: "string"
                        code:
                          type: "string"
                        projectId:
                          type: "string"
                    orderUsed:
                      type: "boolean"
                    orderItemUsed:
                      type: "boolean"
                    orderItemIds:
                      type: "array"
                      items:
                        type: "string"
      name: "CouponDiscountCalcResult>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.get"
  summary: "券详情查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          projectId:
            type: "string"
          code:
            type: "string"
          showProject:
            type: "boolean"
          showSelectorData:
            type: "boolean"
      name: "CouponGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              includeShops:
                type: "array"
                items:
                  type: "string"
              excludeShops:
                type: "array"
                items:
                  type: "string"
              includeGoods:
                type: "array"
                items:
                  type: "string"
              excludeGoods:
                type: "array"
                items:
                  type: "string"
              allIncludeGoods:
                type: "array"
                items:
                  type: "string"
              anyExcludeGoods:
                type: "array"
                items:
                  type: "string"
              id:
                type: "string"
              projectId:
                type: "string"
              code:
                type: "string"
              programId:
                type: "string"
              grantAt:
                type: "string"
                format: "date-time"
              effectiveAt:
                type: "string"
                format: "date-time"
              useAt:
                type: "string"
                format: "date-time"
              expiredAt:
                type: "string"
                format: "date-time"
              status:
                type: "string"
              useCountRestrict:
                type: "integer"
                format: "int64"
              usedCount:
                type: "integer"
                format: "int64"
              ruleText:
                type: "string"
              couponName:
                type: "string"
              projectType:
                type: "string"
              reductAmount:
                type: "number"
              discountRate:
                type: "number"
              thresholdValue:
                type: "number"
              denomination:
                type: "number"
              grantReason:
                type: "string"
              grantShop:
                type: "string"
              extension:
                type: "object"
              project:
                type: "object"
                properties:
                  includeShops:
                    type: "array"
                    items:
                      type: "string"
                  excludeShops:
                    type: "array"
                    items:
                      type: "string"
                  includeGoods:
                    type: "array"
                    items:
                      type: "string"
                  excludeGoods:
                    type: "array"
                    items:
                      type: "string"
                  allIncludeGoods:
                    type: "array"
                    items:
                      type: "string"
                  anyExcludeGoods:
                    type: "array"
                    items:
                      type: "string"
                  id:
                    type: "string"
                  branch:
                    type: "string"
                  title:
                    type: "string"
                  kind:
                    type: "string"
                  campaignTitle:
                    type: "string"
                  description:
                    type: "string"
                  status:
                    type: "string"
                  restrict:
                    type: "object"
                    properties:
                      shopsRefType:
                        type: "string"
                      shopsRef:
                        type: "string"
                      platformsRef:
                        type: "string"
                      goodsRefType:
                        type: "string"
                      goodsRef:
                        type: "string"
                  grantRestrict:
                    type: "object"
                    properties:
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                      platformsRef:
                        type: "string"
                      invokeNum:
                        type: "integer"
                        format: "int32"
                      invokeNumByUserPerDay:
                        type: "integer"
                        format: "int32"
                      invokeNumByPerDay:
                        type: "integer"
                        format: "int32"
                  useRestrict:
                    type: "object"
                    properties:
                      shopsRefType:
                        type: "string"
                      shopsRef:
                        type: "string"
                      platformsRef:
                        type: "string"
                      goodsRefType:
                        type: "string"
                      goodsRef:
                        type: "string"
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                      invokeNumByUserPerDay:
                        type: "integer"
                        format: "int32"
                      wait:
                        type: "object"
                        properties:
                          year:
                            type: "integer"
                            format: "int32"
                          quarter:
                            type: "integer"
                            format: "int32"
                          month:
                            type: "integer"
                            format: "int32"
                          week:
                            type: "integer"
                            format: "int32"
                          day:
                            type: "integer"
                            format: "int32"
                          hour:
                            type: "integer"
                            format: "int32"
                          minute:
                            type: "integer"
                            format: "int32"
                          second:
                            type: "integer"
                            format: "int32"
                      timeType:
                        type: "string"
                      timeCycle:
                        type: "object"
                        properties:
                          value:
                            type: "array"
                            items:
                              type: "integer"
                              format: "int32"
                          unit:
                            type: "string"
                          timeRange:
                            type: "string"
                          cycleStartTime:
                            type: "string"
                            format: "time"
                          cycleEndTime:
                            type: "string"
                            format: "time"
                  cancelUseRestrict:
                    type: "object"
                    properties:
                      invokeNum:
                        type: "integer"
                        format: "int32"
                      invokeNumByUserPerDay:
                        type: "integer"
                        format: "int32"
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                  updateAt:
                    type: "string"
                    format: "date-time"
                  maxQuantity:
                    type: "integer"
                    format: "int64"
                  useCountRestrict:
                    type: "integer"
                    format: "int64"
                  transferable:
                    type: "boolean"
                  type:
                    type: "string"
                  discount:
                    type: "object"
                    properties:
                      mode:
                        type: "string"
                      scope:
                        type: "string"
                      amount:
                        type: "object"
                        properties:
                          type:
                            type: "string"
                          value:
                            type: "number"
                          discountValue:
                            type: "number"
                          unit:
                            type: "string"
                          min:
                            type: "number"
                          max:
                            type: "number"
                          threshold:
                            type: "object"
                            properties:
                              type:
                                type: "string"
                              value:
                                type: "number"
                              maxValue:
                                type: "number"
                              num:
                                type: "integer"
                                format: "int32"
                              maxNum:
                                type: "integer"
                                format: "int32"
                              fixedSteps:
                                type: "array"
                                items:
                                  type: "object"
                                  properties:
                                    value:
                                      type: "number"
                                    threshold:
                                      type: "number"
                              steps:
                                type: "array"
                                items:
                                  type: "object"
                                  properties:
                                    value:
                                      type: "number"
                                    threshold:
                                      type: "number"
                              goodsMinValue:
                                type: "number"
                      minAmount:
                        type: "number"
                      maxAmount:
                        type: "number"
                      maxNum:
                        type: "integer"
                        format: "int32"
                  exchange:
                    type: "object"
                    properties:
                      goods:
                        type: "string"
                      num:
                        type: "integer"
                        format: "int32"
                      maxPerGoods:
                        type: "integer"
                        format: "int32"
                  maxOverlayNum:
                    type: "integer"
                    format: "int32"
                  combinationEnabled:
                    type: "boolean"
                  grantedCount:
                    type: "integer"
                    format: "int64"
                  usedCount:
                    type: "integer"
                    format: "int64"
                  reminderBeforeExpirationEnable:
                    type: "boolean"
                  reminderBeforeExpiration:
                    type: "object"
                    properties:
                      year:
                        type: "integer"
                        format: "int32"
                      quarter:
                        type: "integer"
                        format: "int32"
                      month:
                        type: "integer"
                        format: "int32"
                      week:
                        type: "integer"
                        format: "int32"
                      day:
                        type: "integer"
                        format: "int32"
                      hour:
                        type: "integer"
                        format: "int32"
                      minute:
                        type: "integer"
                        format: "int32"
                      second:
                        type: "integer"
                        format: "int32"
                  effectPeriod:
                    type: "object"
                    properties:
                      type:
                        type: "string"
                      fixed:
                        type: "string"
                        format: "date-time"
                      relatives:
                        type: "array"
                        items:
                          type: "object"
                          properties:
                            scene:
                              type: "string"
                            duration:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                            toDayBegin:
                              type: "boolean"
                            toDayEnd:
                              type: "boolean"
                            fixedPart:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                  expiredPeriod:
                    type: "object"
                    properties:
                      type:
                        type: "string"
                      fixed:
                        type: "string"
                        format: "date-time"
                      relatives:
                        type: "array"
                        items:
                          type: "object"
                          properties:
                            scene:
                              type: "string"
                            duration:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                            toDayBegin:
                              type: "boolean"
                            toDayEnd:
                              type: "boolean"
                            fixedPart:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                  extData:
                    type: "object"
                  template:
                    type: "string"
                  codeMaker:
                    type: "string"
                  effectRestrict:
                    type: "object"
                    properties:
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                  expireRestrict:
                    type: "object"
                    properties:
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                  previous:
                    type: "string"
                  program:
                    type: "string"
                  subOrderShareDiscount:
                    type: "boolean"
                  combinationType:
                    type: "string"
                  storeDirectDistribution:
                    type: "boolean"
                  transferRestrict:
                    type: "object"
                    properties:
                      invokeNum:
                        type: "integer"
                        format: "int32"
                      startAt:
                        type: "string"
                        format: "date-time"
                      endAt:
                        type: "string"
                        format: "date-time"
                  transferPeriod:
                    type: "object"
                    properties:
                      type:
                        type: "string"
                      relatives:
                        type: "array"
                        items:
                          type: "object"
                          properties:
                            scene:
                              type: "string"
                            duration:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                            toDayBegin:
                              type: "boolean"
                            toDayEnd:
                              type: "boolean"
                            fixedPart:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                  weiMob:
                    type: "object"
                    properties:
                      customerDirectReceive:
                        type: "boolean"
                      activityPublish:
                        type: "boolean"
                      enterpriseAssistant:
                        type: "boolean"
                      merchantPublish:
                        type: "boolean"
                      shoppingPublish:
                        type: "boolean"
                      customerListPublish:
                        type: "boolean"
                      servicePublish:
                        type: "boolean"
                      canShare:
                        type: "boolean"
                      canStoreLaunch:
                        type: "boolean"
                      includeStoreGoods:
                        type: "boolean"
                      isAllUseScene:
                        type: "boolean"
                      shoppingMallSceneList:
                        type: "string"
                      canUseWithOtherDiscount:
                        type: "boolean"
                      shoppingMallDiscount:
                        type: "string"
                  weChatPayCouponStocks:
                    type: "object"
                    properties:
                      extension:
                        type: "object"
                      appId:
                        type: "string"
                      belongMerchant:
                        type: "string"
                      invokeNumByUserPerDay:
                        type: "integer"
                        format: "int32"
                      naturalPersonLimit:
                        type: "boolean"
                      preventApiAbuse:
                        type: "boolean"
                      merchantName:
                        type: "string"
                      backgroundColor:
                        type: "string"
                      goodsTag:
                        type: "string"
                      limitPay:
                        type: "string"
                      limitCardName:
                        type: "string"
                      bin:
                        type: "string"
                      tradeType:
                        type: "string"
                      noCash:
                        type: "boolean"
                      availableMchids:
                        type: "string"
                  couponCost:
                    type: "object"
                    properties:
                      couponCostType:
                        type: "string"
                      couponCost:
                        type: "number"
                      orgShare:
                        type: "object"
                        properties:
                          headquarters:
                            type: "number"
                          franchisee:
                            type: "number"
                          brand:
                            type: "number"
                          branch:
                            type: "number"
                          dealer:
                            type: "number"
                          shops:
                            type: "number"
                  positivePriceGoods:
                    type: "boolean"
                  payBenefit:
                    type: "object"
                    properties:
                      isPayment:
                        type: "boolean"
                  storeLimitNum:
                    type: "integer"
                    format: "int32"
                  orderDeductList:
                    type: "string"
                  materialImage:
                    type: "object"
                    properties:
                      cardVoucherListImage:
                        type: "string"
                      cardVoucherDetailImage:
                        type: "string"
                      brandLogo:
                        type: "string"
                      goodsImage:
                        type: "string"
                  createNow:
                    type: "boolean"
                  activateNow:
                    type: "boolean"
                  subject:
                    type: "string"
              transferState:
                type: "string"
              useShop:
                type: "string"
              useOrderId:
                type: "string"
              activateAt:
                type: "string"
                format: "date-time"
              grantPlatform:
                type: "string"
              scene:
                type: "string"
              lastState:
                type: "string"
              receiver:
                type: "string"
              transferredCount:
                type: "integer"
                format: "int32"
              transferredCountRestrict:
                type: "integer"
                format: "int32"
              usableOrderItemIds:
                type: "array"
                items:
                  type: "string"
              unavailabilityReason:
                type: "string"
              memberId:
                type: "string"
      name: "CouponGetResult>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.grant"
  summary: "卡券发放"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          projectId:
            type: "string"
          grantNum:
            type: "integer"
            format: "int32"
          grantPlatform:
            type: "string"
          grantShop:
            type: "string"
          grantReason:
            type: "string"
          sourceModule:
            type: "string"
          marketingNodeId:
            type: "string"
          marketingActivityId:
            type: "string"
      name: "CouponGrantParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "Coupon>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.grantRepeal"
  summary: "卡券作废"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "CouponGrantRepealParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "Coupon>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.list"
  summary: "优惠券列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          projectId:
            type: "string"
          state:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          queryParams:
            type: "string"
          showProject:
            type: "boolean"
          showSelectorData:
            type: "boolean"
      name: "CouponListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              total:
                type: "integer"
                format: "int64"
              data:
                type: "array"
                items:
                  type: "object"
                  properties:
                    includeShops:
                      type: "array"
                      items:
                        type: "string"
                    excludeShops:
                      type: "array"
                      items:
                        type: "string"
                    includeGoods:
                      type: "array"
                      items:
                        type: "string"
                    excludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    allIncludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    anyExcludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    id:
                      type: "string"
                    projectId:
                      type: "string"
                    code:
                      type: "string"
                    programId:
                      type: "string"
                    grantAt:
                      type: "string"
                      format: "date-time"
                    effectiveAt:
                      type: "string"
                      format: "date-time"
                    useAt:
                      type: "string"
                      format: "date-time"
                    expiredAt:
                      type: "string"
                      format: "date-time"
                    status:
                      type: "string"
                    useCountRestrict:
                      type: "integer"
                      format: "int64"
                    usedCount:
                      type: "integer"
                      format: "int64"
                    ruleText:
                      type: "string"
                    couponName:
                      type: "string"
                    projectType:
                      type: "string"
                    reductAmount:
                      type: "number"
                    discountRate:
                      type: "number"
                    thresholdValue:
                      type: "number"
                    denomination:
                      type: "number"
                    grantReason:
                      type: "string"
                    grantShop:
                      type: "string"
                    extension:
                      type: "object"
                    project:
                      type: "object"
                      properties:
                        includeShops:
                          type: "array"
                          items:
                            type: "string"
                        excludeShops:
                          type: "array"
                          items:
                            type: "string"
                        includeGoods:
                          type: "array"
                          items:
                            type: "string"
                        excludeGoods:
                          type: "array"
                          items:
                            type: "string"
                        allIncludeGoods:
                          type: "array"
                          items:
                            type: "string"
                        anyExcludeGoods:
                          type: "array"
                          items:
                            type: "string"
                        id:
                          type: "string"
                        branch:
                          type: "string"
                        title:
                          type: "string"
                        kind:
                          type: "string"
                        campaignTitle:
                          type: "string"
                        description:
                          type: "string"
                        status:
                          type: "string"
                        restrict:
                          type: "object"
                          properties:
                            shopsRefType:
                              type: "string"
                            shopsRef:
                              type: "string"
                            platformsRef:
                              type: "string"
                            goodsRefType:
                              type: "string"
                            goodsRef:
                              type: "string"
                        grantRestrict:
                          type: "object"
                          properties:
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                            platformsRef:
                              type: "string"
                            invokeNum:
                              type: "integer"
                              format: "int32"
                            invokeNumByUserPerDay:
                              type: "integer"
                              format: "int32"
                            invokeNumByPerDay:
                              type: "integer"
                              format: "int32"
                        useRestrict:
                          type: "object"
                          properties:
                            shopsRefType:
                              type: "string"
                            shopsRef:
                              type: "string"
                            platformsRef:
                              type: "string"
                            goodsRefType:
                              type: "string"
                            goodsRef:
                              type: "string"
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                            invokeNumByUserPerDay:
                              type: "integer"
                              format: "int32"
                            wait:
                              type: "object"
                              properties:
                                year:
                                  type: "integer"
                                  format: "int32"
                                quarter:
                                  type: "integer"
                                  format: "int32"
                                month:
                                  type: "integer"
                                  format: "int32"
                                week:
                                  type: "integer"
                                  format: "int32"
                                day:
                                  type: "integer"
                                  format: "int32"
                                hour:
                                  type: "integer"
                                  format: "int32"
                                minute:
                                  type: "integer"
                                  format: "int32"
                                second:
                                  type: "integer"
                                  format: "int32"
                            timeType:
                              type: "string"
                            timeCycle:
                              type: "object"
                              properties:
                                value:
                                  type: "array"
                                  items:
                                    type: "integer"
                                    format: "int32"
                                unit:
                                  type: "string"
                                timeRange:
                                  type: "string"
                                cycleStartTime:
                                  type: "string"
                                  format: "time"
                                cycleEndTime:
                                  type: "string"
                                  format: "time"
                        cancelUseRestrict:
                          type: "object"
                          properties:
                            invokeNum:
                              type: "integer"
                              format: "int32"
                            invokeNumByUserPerDay:
                              type: "integer"
                              format: "int32"
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                        updateAt:
                          type: "string"
                          format: "date-time"
                        maxQuantity:
                          type: "integer"
                          format: "int64"
                        useCountRestrict:
                          type: "integer"
                          format: "int64"
                        transferable:
                          type: "boolean"
                        type:
                          type: "string"
                        discount:
                          type: "object"
                          properties:
                            mode:
                              type: "string"
                            scope:
                              type: "string"
                            amount:
                              type: "object"
                              properties:
                                type:
                                  type: "string"
                                value:
                                  type: "number"
                                discountValue:
                                  type: "number"
                                unit:
                                  type: "string"
                                min:
                                  type: "number"
                                max:
                                  type: "number"
                                threshold:
                                  type: "object"
                                  properties:
                                    type:
                                      type: "string"
                                    value:
                                      type: "number"
                                    maxValue:
                                      type: "number"
                                    num:
                                      type: "integer"
                                      format: "int32"
                                    maxNum:
                                      type: "integer"
                                      format: "int32"
                                    fixedSteps:
                                      type: "array"
                                      items:
                                        type: "object"
                                        properties:
                                          value:
                                            type: "number"
                                          threshold:
                                            type: "number"
                                    steps:
                                      type: "array"
                                      items:
                                        type: "object"
                                        properties:
                                          value:
                                            type: "number"
                                          threshold:
                                            type: "number"
                                    goodsMinValue:
                                      type: "number"
                            minAmount:
                              type: "number"
                            maxAmount:
                              type: "number"
                            maxNum:
                              type: "integer"
                              format: "int32"
                        exchange:
                          type: "object"
                          properties:
                            goods:
                              type: "string"
                            num:
                              type: "integer"
                              format: "int32"
                            maxPerGoods:
                              type: "integer"
                              format: "int32"
                        maxOverlayNum:
                          type: "integer"
                          format: "int32"
                        combinationEnabled:
                          type: "boolean"
                        grantedCount:
                          type: "integer"
                          format: "int64"
                        usedCount:
                          type: "integer"
                          format: "int64"
                        reminderBeforeExpirationEnable:
                          type: "boolean"
                        reminderBeforeExpiration:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        effectPeriod:
                          type: "object"
                          properties:
                            type:
                              type: "string"
                            fixed:
                              type: "string"
                              format: "date-time"
                            relatives:
                              type: "array"
                              items:
                                type: "object"
                                properties:
                                  scene:
                                    type: "string"
                                  duration:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                                  toDayBegin:
                                    type: "boolean"
                                  toDayEnd:
                                    type: "boolean"
                                  fixedPart:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                        expiredPeriod:
                          type: "object"
                          properties:
                            type:
                              type: "string"
                            fixed:
                              type: "string"
                              format: "date-time"
                            relatives:
                              type: "array"
                              items:
                                type: "object"
                                properties:
                                  scene:
                                    type: "string"
                                  duration:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                                  toDayBegin:
                                    type: "boolean"
                                  toDayEnd:
                                    type: "boolean"
                                  fixedPart:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                        extData:
                          type: "object"
                        template:
                          type: "string"
                        codeMaker:
                          type: "string"
                        effectRestrict:
                          type: "object"
                          properties:
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                        expireRestrict:
                          type: "object"
                          properties:
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                        previous:
                          type: "string"
                        program:
                          type: "string"
                        subOrderShareDiscount:
                          type: "boolean"
                        combinationType:
                          type: "string"
                        storeDirectDistribution:
                          type: "boolean"
                        transferRestrict:
                          type: "object"
                          properties:
                            invokeNum:
                              type: "integer"
                              format: "int32"
                            startAt:
                              type: "string"
                              format: "date-time"
                            endAt:
                              type: "string"
                              format: "date-time"
                        transferPeriod:
                          type: "object"
                          properties:
                            type:
                              type: "string"
                            relatives:
                              type: "array"
                              items:
                                type: "object"
                                properties:
                                  scene:
                                    type: "string"
                                  duration:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                                  toDayBegin:
                                    type: "boolean"
                                  toDayEnd:
                                    type: "boolean"
                                  fixedPart:
                                    type: "object"
                                    properties:
                                      year:
                                        type: "integer"
                                        format: "int32"
                                      quarter:
                                        type: "integer"
                                        format: "int32"
                                      month:
                                        type: "integer"
                                        format: "int32"
                                      week:
                                        type: "integer"
                                        format: "int32"
                                      day:
                                        type: "integer"
                                        format: "int32"
                                      hour:
                                        type: "integer"
                                        format: "int32"
                                      minute:
                                        type: "integer"
                                        format: "int32"
                                      second:
                                        type: "integer"
                                        format: "int32"
                        weiMob:
                          type: "object"
                          properties:
                            customerDirectReceive:
                              type: "boolean"
                            activityPublish:
                              type: "boolean"
                            enterpriseAssistant:
                              type: "boolean"
                            merchantPublish:
                              type: "boolean"
                            shoppingPublish:
                              type: "boolean"
                            customerListPublish:
                              type: "boolean"
                            servicePublish:
                              type: "boolean"
                            canShare:
                              type: "boolean"
                            canStoreLaunch:
                              type: "boolean"
                            includeStoreGoods:
                              type: "boolean"
                            isAllUseScene:
                              type: "boolean"
                            shoppingMallSceneList:
                              type: "string"
                            canUseWithOtherDiscount:
                              type: "boolean"
                            shoppingMallDiscount:
                              type: "string"
                        weChatPayCouponStocks:
                          type: "object"
                          properties:
                            extension:
                              type: "object"
                            appId:
                              type: "string"
                            belongMerchant:
                              type: "string"
                            invokeNumByUserPerDay:
                              type: "integer"
                              format: "int32"
                            naturalPersonLimit:
                              type: "boolean"
                            preventApiAbuse:
                              type: "boolean"
                            merchantName:
                              type: "string"
                            backgroundColor:
                              type: "string"
                            goodsTag:
                              type: "string"
                            limitPay:
                              type: "string"
                            limitCardName:
                              type: "string"
                            bin:
                              type: "string"
                            tradeType:
                              type: "string"
                            noCash:
                              type: "boolean"
                            availableMchids:
                              type: "string"
                        couponCost:
                          type: "object"
                          properties:
                            couponCostType:
                              type: "string"
                            couponCost:
                              type: "number"
                            orgShare:
                              type: "object"
                              properties:
                                headquarters:
                                  type: "number"
                                franchisee:
                                  type: "number"
                                brand:
                                  type: "number"
                                branch:
                                  type: "number"
                                dealer:
                                  type: "number"
                                shops:
                                  type: "number"
                        positivePriceGoods:
                          type: "boolean"
                        payBenefit:
                          type: "object"
                          properties:
                            isPayment:
                              type: "boolean"
                        storeLimitNum:
                          type: "integer"
                          format: "int32"
                        orderDeductList:
                          type: "string"
                        materialImage:
                          type: "object"
                          properties:
                            cardVoucherListImage:
                              type: "string"
                            cardVoucherDetailImage:
                              type: "string"
                            brandLogo:
                              type: "string"
                            goodsImage:
                              type: "string"
                        createNow:
                          type: "boolean"
                        activateNow:
                          type: "boolean"
                        subject:
                          type: "string"
                    transferState:
                      type: "string"
                    useShop:
                      type: "string"
                    useOrderId:
                      type: "string"
                    activateAt:
                      type: "string"
                      format: "date-time"
                    grantPlatform:
                      type: "string"
                    scene:
                      type: "string"
                    lastState:
                      type: "string"
                    receiver:
                      type: "string"
                    transferredCount:
                      type: "integer"
                      format: "int32"
                    transferredCountRestrict:
                      type: "integer"
                      format: "int32"
                    usableOrderItemIds:
                      type: "array"
                      items:
                        type: "string"
                    unavailabilityReason:
                      type: "string"
                    memberId:
                      type: "string"
      name: "CouponGetResult>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.lock"
  summary: "卡券锁定"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "CouponLockParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "Coupon>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.projectGet"
  summary: "券项目查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          projectId:
            type: "string"
          useCache:
            type: "boolean"
          showSelectorData:
            type: "boolean"
      name: "CouponProjectGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              includeShops:
                type: "array"
                items:
                  type: "string"
              excludeShops:
                type: "array"
                items:
                  type: "string"
              includeGoods:
                type: "array"
                items:
                  type: "string"
              excludeGoods:
                type: "array"
                items:
                  type: "string"
              allIncludeGoods:
                type: "array"
                items:
                  type: "string"
              anyExcludeGoods:
                type: "array"
                items:
                  type: "string"
              id:
                type: "string"
              branch:
                type: "string"
              title:
                type: "string"
              kind:
                type: "string"
              campaignTitle:
                type: "string"
              description:
                type: "string"
              status:
                type: "string"
              restrict:
                type: "object"
                properties:
                  shopsRefType:
                    type: "string"
                  shopsRef:
                    type: "string"
                  platformsRef:
                    type: "string"
                  goodsRefType:
                    type: "string"
                  goodsRef:
                    type: "string"
              grantRestrict:
                type: "object"
                properties:
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
                  platformsRef:
                    type: "string"
                  invokeNum:
                    type: "integer"
                    format: "int32"
                  invokeNumByUserPerDay:
                    type: "integer"
                    format: "int32"
                  invokeNumByPerDay:
                    type: "integer"
                    format: "int32"
              useRestrict:
                type: "object"
                properties:
                  shopsRefType:
                    type: "string"
                  shopsRef:
                    type: "string"
                  platformsRef:
                    type: "string"
                  goodsRefType:
                    type: "string"
                  goodsRef:
                    type: "string"
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
                  invokeNumByUserPerDay:
                    type: "integer"
                    format: "int32"
                  wait:
                    type: "object"
                    properties:
                      year:
                        type: "integer"
                        format: "int32"
                      quarter:
                        type: "integer"
                        format: "int32"
                      month:
                        type: "integer"
                        format: "int32"
                      week:
                        type: "integer"
                        format: "int32"
                      day:
                        type: "integer"
                        format: "int32"
                      hour:
                        type: "integer"
                        format: "int32"
                      minute:
                        type: "integer"
                        format: "int32"
                      second:
                        type: "integer"
                        format: "int32"
                  timeType:
                    type: "string"
                  timeCycle:
                    type: "object"
                    properties:
                      value:
                        type: "array"
                        items:
                          type: "integer"
                          format: "int32"
                      unit:
                        type: "string"
                      timeRange:
                        type: "string"
                      cycleStartTime:
                        type: "string"
                        format: "time"
                      cycleEndTime:
                        type: "string"
                        format: "time"
              cancelUseRestrict:
                type: "object"
                properties:
                  invokeNum:
                    type: "integer"
                    format: "int32"
                  invokeNumByUserPerDay:
                    type: "integer"
                    format: "int32"
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
              updateAt:
                type: "string"
                format: "date-time"
              maxQuantity:
                type: "integer"
                format: "int64"
              useCountRestrict:
                type: "integer"
                format: "int64"
              transferable:
                type: "boolean"
              type:
                type: "string"
              discount:
                type: "object"
                properties:
                  mode:
                    type: "string"
                  scope:
                    type: "string"
                  amount:
                    type: "object"
                    properties:
                      type:
                        type: "string"
                      value:
                        type: "number"
                      discountValue:
                        type: "number"
                      unit:
                        type: "string"
                      min:
                        type: "number"
                      max:
                        type: "number"
                      threshold:
                        type: "object"
                        properties:
                          type:
                            type: "string"
                          value:
                            type: "number"
                          maxValue:
                            type: "number"
                          num:
                            type: "integer"
                            format: "int32"
                          maxNum:
                            type: "integer"
                            format: "int32"
                          fixedSteps:
                            type: "array"
                            items:
                              type: "object"
                              properties:
                                value:
                                  type: "number"
                                threshold:
                                  type: "number"
                          steps:
                            type: "array"
                            items:
                              type: "object"
                              properties:
                                value:
                                  type: "number"
                                threshold:
                                  type: "number"
                          goodsMinValue:
                            type: "number"
                  minAmount:
                    type: "number"
                  maxAmount:
                    type: "number"
                  maxNum:
                    type: "integer"
                    format: "int32"
              exchange:
                type: "object"
                properties:
                  goods:
                    type: "string"
                  num:
                    type: "integer"
                    format: "int32"
                  maxPerGoods:
                    type: "integer"
                    format: "int32"
              maxOverlayNum:
                type: "integer"
                format: "int32"
              combinationEnabled:
                type: "boolean"
              grantedCount:
                type: "integer"
                format: "int64"
              usedCount:
                type: "integer"
                format: "int64"
              reminderBeforeExpirationEnable:
                type: "boolean"
              reminderBeforeExpiration:
                type: "object"
                properties:
                  year:
                    type: "integer"
                    format: "int32"
                  quarter:
                    type: "integer"
                    format: "int32"
                  month:
                    type: "integer"
                    format: "int32"
                  week:
                    type: "integer"
                    format: "int32"
                  day:
                    type: "integer"
                    format: "int32"
                  hour:
                    type: "integer"
                    format: "int32"
                  minute:
                    type: "integer"
                    format: "int32"
                  second:
                    type: "integer"
                    format: "int32"
              effectPeriod:
                type: "object"
                properties:
                  type:
                    type: "string"
                  fixed:
                    type: "string"
                    format: "date-time"
                  relatives:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        scene:
                          type: "string"
                        duration:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        toDayBegin:
                          type: "boolean"
                        toDayEnd:
                          type: "boolean"
                        fixedPart:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
              expiredPeriod:
                type: "object"
                properties:
                  type:
                    type: "string"
                  fixed:
                    type: "string"
                    format: "date-time"
                  relatives:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        scene:
                          type: "string"
                        duration:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        toDayBegin:
                          type: "boolean"
                        toDayEnd:
                          type: "boolean"
                        fixedPart:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
              extData:
                type: "object"
              template:
                type: "string"
              codeMaker:
                type: "string"
              effectRestrict:
                type: "object"
                properties:
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
              expireRestrict:
                type: "object"
                properties:
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
              previous:
                type: "string"
              program:
                type: "string"
              subOrderShareDiscount:
                type: "boolean"
              combinationType:
                type: "string"
              storeDirectDistribution:
                type: "boolean"
              transferRestrict:
                type: "object"
                properties:
                  invokeNum:
                    type: "integer"
                    format: "int32"
                  startAt:
                    type: "string"
                    format: "date-time"
                  endAt:
                    type: "string"
                    format: "date-time"
              transferPeriod:
                type: "object"
                properties:
                  type:
                    type: "string"
                  relatives:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        scene:
                          type: "string"
                        duration:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        toDayBegin:
                          type: "boolean"
                        toDayEnd:
                          type: "boolean"
                        fixedPart:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
              weiMob:
                type: "object"
                properties:
                  customerDirectReceive:
                    type: "boolean"
                  activityPublish:
                    type: "boolean"
                  enterpriseAssistant:
                    type: "boolean"
                  merchantPublish:
                    type: "boolean"
                  shoppingPublish:
                    type: "boolean"
                  customerListPublish:
                    type: "boolean"
                  servicePublish:
                    type: "boolean"
                  canShare:
                    type: "boolean"
                  canStoreLaunch:
                    type: "boolean"
                  includeStoreGoods:
                    type: "boolean"
                  isAllUseScene:
                    type: "boolean"
                  shoppingMallSceneList:
                    type: "string"
                  canUseWithOtherDiscount:
                    type: "boolean"
                  shoppingMallDiscount:
                    type: "string"
              weChatPayCouponStocks:
                type: "object"
                properties:
                  extension:
                    type: "object"
                  appId:
                    type: "string"
                  belongMerchant:
                    type: "string"
                  invokeNumByUserPerDay:
                    type: "integer"
                    format: "int32"
                  naturalPersonLimit:
                    type: "boolean"
                  preventApiAbuse:
                    type: "boolean"
                  merchantName:
                    type: "string"
                  backgroundColor:
                    type: "string"
                  goodsTag:
                    type: "string"
                  limitPay:
                    type: "string"
                  limitCardName:
                    type: "string"
                  bin:
                    type: "string"
                  tradeType:
                    type: "string"
                  noCash:
                    type: "boolean"
                  availableMchids:
                    type: "string"
              couponCost:
                type: "object"
                properties:
                  couponCostType:
                    type: "string"
                  couponCost:
                    type: "number"
                  orgShare:
                    type: "object"
                    properties:
                      headquarters:
                        type: "number"
                      franchisee:
                        type: "number"
                      brand:
                        type: "number"
                      branch:
                        type: "number"
                      dealer:
                        type: "number"
                      shops:
                        type: "number"
              positivePriceGoods:
                type: "boolean"
              payBenefit:
                type: "object"
                properties:
                  isPayment:
                    type: "boolean"
              storeLimitNum:
                type: "integer"
                format: "int32"
              orderDeductList:
                type: "string"
              materialImage:
                type: "object"
                properties:
                  cardVoucherListImage:
                    type: "string"
                  cardVoucherDetailImage:
                    type: "string"
                  brandLogo:
                    type: "string"
                  goodsImage:
                    type: "string"
              createNow:
                type: "boolean"
              activateNow:
                type: "boolean"
              subject:
                type: "string"
      name: "CouponProject>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.projectList"
  summary: "券项目列表查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          queryParams:
            type: "string"
          sortBy:
            type: "string"
          sortType:
            type: "string"
      name: "CouponProjectListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              total:
                type: "integer"
                format: "int64"
              data:
                type: "array"
                items:
                  type: "object"
                  properties:
                    includeShops:
                      type: "array"
                      items:
                        type: "string"
                    excludeShops:
                      type: "array"
                      items:
                        type: "string"
                    includeGoods:
                      type: "array"
                      items:
                        type: "string"
                    excludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    allIncludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    anyExcludeGoods:
                      type: "array"
                      items:
                        type: "string"
                    id:
                      type: "string"
                    branch:
                      type: "string"
                    title:
                      type: "string"
                    kind:
                      type: "string"
                    campaignTitle:
                      type: "string"
                    description:
                      type: "string"
                    status:
                      type: "string"
                    restrict:
                      type: "object"
                      properties:
                        shopsRefType:
                          type: "string"
                        shopsRef:
                          type: "string"
                        platformsRef:
                          type: "string"
                        goodsRefType:
                          type: "string"
                        goodsRef:
                          type: "string"
                    grantRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                        platformsRef:
                          type: "string"
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        invokeNumByPerDay:
                          type: "integer"
                          format: "int32"
                    useRestrict:
                      type: "object"
                      properties:
                        shopsRefType:
                          type: "string"
                        shopsRef:
                          type: "string"
                        platformsRef:
                          type: "string"
                        goodsRefType:
                          type: "string"
                        goodsRef:
                          type: "string"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        wait:
                          type: "object"
                          properties:
                            year:
                              type: "integer"
                              format: "int32"
                            quarter:
                              type: "integer"
                              format: "int32"
                            month:
                              type: "integer"
                              format: "int32"
                            week:
                              type: "integer"
                              format: "int32"
                            day:
                              type: "integer"
                              format: "int32"
                            hour:
                              type: "integer"
                              format: "int32"
                            minute:
                              type: "integer"
                              format: "int32"
                            second:
                              type: "integer"
                              format: "int32"
                        timeType:
                          type: "string"
                        timeCycle:
                          type: "object"
                          properties:
                            value:
                              type: "array"
                              items:
                                type: "integer"
                                format: "int32"
                            unit:
                              type: "string"
                            timeRange:
                              type: "string"
                            cycleStartTime:
                              type: "string"
                              format: "time"
                            cycleEndTime:
                              type: "string"
                              format: "time"
                    cancelUseRestrict:
                      type: "object"
                      properties:
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    updateAt:
                      type: "string"
                      format: "date-time"
                    maxQuantity:
                      type: "integer"
                      format: "int64"
                    useCountRestrict:
                      type: "integer"
                      format: "int64"
                    transferable:
                      type: "boolean"
                    type:
                      type: "string"
                    discount:
                      type: "object"
                      properties:
                        mode:
                          type: "string"
                        scope:
                          type: "string"
                        amount:
                          type: "object"
                          properties:
                            type:
                              type: "string"
                            value:
                              type: "number"
                            discountValue:
                              type: "number"
                            unit:
                              type: "string"
                            min:
                              type: "number"
                            max:
                              type: "number"
                            threshold:
                              type: "object"
                              properties:
                                type:
                                  type: "string"
                                value:
                                  type: "number"
                                maxValue:
                                  type: "number"
                                num:
                                  type: "integer"
                                  format: "int32"
                                maxNum:
                                  type: "integer"
                                  format: "int32"
                                fixedSteps:
                                  type: "array"
                                  items:
                                    type: "object"
                                    properties:
                                      value:
                                        type: "number"
                                      threshold:
                                        type: "number"
                                steps:
                                  type: "array"
                                  items:
                                    type: "object"
                                    properties:
                                      value:
                                        type: "number"
                                      threshold:
                                        type: "number"
                                goodsMinValue:
                                  type: "number"
                        minAmount:
                          type: "number"
                        maxAmount:
                          type: "number"
                        maxNum:
                          type: "integer"
                          format: "int32"
                    exchange:
                      type: "object"
                      properties:
                        goods:
                          type: "string"
                        num:
                          type: "integer"
                          format: "int32"
                        maxPerGoods:
                          type: "integer"
                          format: "int32"
                    maxOverlayNum:
                      type: "integer"
                      format: "int32"
                    combinationEnabled:
                      type: "boolean"
                    grantedCount:
                      type: "integer"
                      format: "int64"
                    usedCount:
                      type: "integer"
                      format: "int64"
                    reminderBeforeExpirationEnable:
                      type: "boolean"
                    reminderBeforeExpiration:
                      type: "object"
                      properties:
                        year:
                          type: "integer"
                          format: "int32"
                        quarter:
                          type: "integer"
                          format: "int32"
                        month:
                          type: "integer"
                          format: "int32"
                        week:
                          type: "integer"
                          format: "int32"
                        day:
                          type: "integer"
                          format: "int32"
                        hour:
                          type: "integer"
                          format: "int32"
                        minute:
                          type: "integer"
                          format: "int32"
                        second:
                          type: "integer"
                          format: "int32"
                    effectPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        fixed:
                          type: "string"
                          format: "date-time"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    expiredPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        fixed:
                          type: "string"
                          format: "date-time"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    extData:
                      type: "object"
                    template:
                      type: "string"
                    codeMaker:
                      type: "string"
                    effectRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    expireRestrict:
                      type: "object"
                      properties:
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    previous:
                      type: "string"
                    program:
                      type: "string"
                    subOrderShareDiscount:
                      type: "boolean"
                    combinationType:
                      type: "string"
                    storeDirectDistribution:
                      type: "boolean"
                    transferRestrict:
                      type: "object"
                      properties:
                        invokeNum:
                          type: "integer"
                          format: "int32"
                        startAt:
                          type: "string"
                          format: "date-time"
                        endAt:
                          type: "string"
                          format: "date-time"
                    transferPeriod:
                      type: "object"
                      properties:
                        type:
                          type: "string"
                        relatives:
                          type: "array"
                          items:
                            type: "object"
                            properties:
                              scene:
                                type: "string"
                              duration:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                              toDayBegin:
                                type: "boolean"
                              toDayEnd:
                                type: "boolean"
                              fixedPart:
                                type: "object"
                                properties:
                                  year:
                                    type: "integer"
                                    format: "int32"
                                  quarter:
                                    type: "integer"
                                    format: "int32"
                                  month:
                                    type: "integer"
                                    format: "int32"
                                  week:
                                    type: "integer"
                                    format: "int32"
                                  day:
                                    type: "integer"
                                    format: "int32"
                                  hour:
                                    type: "integer"
                                    format: "int32"
                                  minute:
                                    type: "integer"
                                    format: "int32"
                                  second:
                                    type: "integer"
                                    format: "int32"
                    weiMob:
                      type: "object"
                      properties:
                        customerDirectReceive:
                          type: "boolean"
                        activityPublish:
                          type: "boolean"
                        enterpriseAssistant:
                          type: "boolean"
                        merchantPublish:
                          type: "boolean"
                        shoppingPublish:
                          type: "boolean"
                        customerListPublish:
                          type: "boolean"
                        servicePublish:
                          type: "boolean"
                        canShare:
                          type: "boolean"
                        canStoreLaunch:
                          type: "boolean"
                        includeStoreGoods:
                          type: "boolean"
                        isAllUseScene:
                          type: "boolean"
                        shoppingMallSceneList:
                          type: "string"
                        canUseWithOtherDiscount:
                          type: "boolean"
                        shoppingMallDiscount:
                          type: "string"
                    weChatPayCouponStocks:
                      type: "object"
                      properties:
                        extension:
                          type: "object"
                        appId:
                          type: "string"
                        belongMerchant:
                          type: "string"
                        invokeNumByUserPerDay:
                          type: "integer"
                          format: "int32"
                        naturalPersonLimit:
                          type: "boolean"
                        preventApiAbuse:
                          type: "boolean"
                        merchantName:
                          type: "string"
                        backgroundColor:
                          type: "string"
                        goodsTag:
                          type: "string"
                        limitPay:
                          type: "string"
                        limitCardName:
                          type: "string"
                        bin:
                          type: "string"
                        tradeType:
                          type: "string"
                        noCash:
                          type: "boolean"
                        availableMchids:
                          type: "string"
                    couponCost:
                      type: "object"
                      properties:
                        couponCostType:
                          type: "string"
                        couponCost:
                          type: "number"
                        orgShare:
                          type: "object"
                          properties:
                            headquarters:
                              type: "number"
                            franchisee:
                              type: "number"
                            brand:
                              type: "number"
                            branch:
                              type: "number"
                            dealer:
                              type: "number"
                            shops:
                              type: "number"
                    positivePriceGoods:
                      type: "boolean"
                    payBenefit:
                      type: "object"
                      properties:
                        isPayment:
                          type: "boolean"
                    storeLimitNum:
                      type: "integer"
                      format: "int32"
                    orderDeductList:
                      type: "string"
                    materialImage:
                      type: "object"
                      properties:
                        cardVoucherListImage:
                          type: "string"
                        cardVoucherDetailImage:
                          type: "string"
                        brandLogo:
                          type: "string"
                        goodsImage:
                          type: "string"
                    createNow:
                      type: "boolean"
                    activateNow:
                      type: "boolean"
                    subject:
                      type: "string"
      name: "CouponProject>>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.receive"
  summary: "卡券受赠"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          holder:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          receiver:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "CouponReceiveParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.returns"
  summary: "卡券退回"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          holder:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "CouponReturnParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.transfer"
  summary: "卡券转赠"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          holder:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          receiver:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "CouponTransferParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.CouponOperation.unlock"
  summary: "卡券解锁"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          atomic:
            type: "boolean"
          extension:
            type: "object"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          coupons:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
          lockTransactionId:
            type: "string"
      name: "CouponUnlockParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "string"
                projectId:
                  type: "string"
                code:
                  type: "string"
                grantAt:
                  type: "string"
                  format: "date-time"
                state:
                  type: "string"
      name: "Coupon>>"
- method: "com.shuyun.apaas.connector.fast.operation.GradeOperation.budget"
  summary: "等级预算"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          gradeBizType:
            type: "string"
          gradeId:
            type: "string"
      name: "GradeBudgetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              gradeId:
                type: "string"
              gradeName:
                type: "string"
              upgradeDifference:
                type: "object"
                properties:
                  logicOperator:
                    type: "string"
                  differences:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        attributeCode:
                          type: "string"
                        attributeName:
                          type: "string"
                        attributeValue:
                          type: "integer"
                          format: "int64"
                        gradeThreshold:
                          type: "integer"
                          format: "int64"
                        difference:
                          type: "integer"
                          format: "int64"
      name: "GradeBudgetResult>"
- method: "com.shuyun.apaas.connector.fast.operation.GradeOperation.get"
  summary: "等级查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          gradeBizType:
            type: "string"
      name: "GradeGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              id:
                type: "integer"
                format: "int64"
              name:
                type: "string"
              effectTime:
                type: "string"
                format: "date-time"
              expiredTime:
                type: "string"
                format: "date-time"
      name: "Grade>"
- method: "com.shuyun.apaas.connector.fast.operation.GradeOperation.metadataGet"
  summary: "等级/勋章元数据(规则可选)"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          gradeBizType:
            type: "string"
          rulesRequired:
            type: "boolean"
      name: "GradeMetadataParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                gradeId:
                  type: "string"
                gradeName:
                  type: "string"
                gradeSort:
                  type: "integer"
                  format: "int32"
                upgradeRule:
                  type: "object"
                  properties:
                    logicOperator:
                      type: "string"
                    rules:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          attributeCode:
                            type: "string"
                          attributeName:
                            type: "string"
                          comparisonOperator:
                            type: "string"
                          gradeThreshold:
                            type: "integer"
                            format: "int64"
                          nextGradeThreshold:
                            type: "integer"
                            format: "int64"
                stayGradeRule:
                  type: "object"
                  properties:
                    logicOperator:
                      type: "string"
                    rules:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          attributeCode:
                            type: "string"
                          attributeName:
                            type: "string"
                          comparisonOperator:
                            type: "string"
                          gradeThreshold:
                            type: "integer"
                            format: "int64"
                          nextGradeThreshold:
                            type: "integer"
                            format: "int64"
      name: "GradeMetadataResult>>"
- method: "com.shuyun.apaas.connector.fast.operation.GradeOperation.recordsGet"
  summary: "等级明细查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          startTime:
            type: "string"
            format: "date-time"
          endTime:
            type: "string"
            format: "date-time"
          sortBy:
            type: "string"
          sortType:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          gradeBizType:
            type: "string"
      name: "GradeRecordsGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberId:
                      type: "string"
                    originalGradeId:
                      type: "integer"
                      format: "int64"
                    originalGradeName:
                      type: "string"
                    currentGradeId:
                      type: "integer"
                      format: "int64"
                    currentGradeName:
                      type: "string"
                    recordType:
                      type: "string"
                    description:
                      type: "string"
                    originalEffectTime:
                      type: "string"
                      format: "date-time"
                    originalExpireTime:
                      type: "string"
                      format: "date-time"
                    currentEffectTime:
                      type: "string"
                      format: "date-time"
                    currentExpireTime:
                      type: "string"
                      format: "date-time"
                    changeTime:
                      type: "string"
                      format: "date-time"
      name: "GradeRecord>>"
- method: "com.shuyun.apaas.connector.fast.operation.MdmOperation.orgSync"
  summary: "组织结构同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          memberType:
            type: "string"
          id:
            type: "string"
          orgId:
            type: "string"
          orgCode:
            type: "string"
          orgName:
            type: "string"
          tier:
            type: "integer"
            format: "int32"
          parentId:
            type: "string"
          orgParentCode:
            type: "string"
          parentName:
            type: "string"
          orderNum:
            type: "integer"
            format: "int32"
          leader:
            type: "integer"
            format: "int32"
          status:
            type: "integer"
            format: "int32"
          createTime:
            type: "string"
            format: "date-time"
          updateTime:
            type: "string"
            format: "date-time"
          lastSync:
            type: "string"
            format: "date-time"
          extension:
            type: "object"
      name: "MdmOrgSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.MdmOperation.productSync"
  summary: "商品同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          memberType:
            type: "string"
          id:
            type: "string"
          channelType:
            type: "string"
          productCode:
            type: "string"
          productName:
            type: "string"
          deptCode:
            type: "string"
          deptName:
            type: "string"
          familyCode:
            type: "string"
          familyName:
            type: "string"
          subFamilyCode:
            type: "string"
          subFamilyName:
            type: "string"
          brand:
            type: "string"
          color:
            type: "string"
          eanCode:
            type: "string"
          material:
            type: "string"
          onSaleYear:
            type: "string"
          onSaledate:
            type: "string"
          picture:
            type: "array"
            items:
              type: "string"
          retailPrice:
            type: "number"
            format: "double"
          season:
            type: "string"
          size:
            type: "string"
          sqId:
            type: "string"
          tagPrice:
            type: "number"
            format: "double"
          productDesc:
            type: "string"
          isValid:
            type: "string"
          createTime:
            type: "string"
            format: "date-time"
          updateTime:
            type: "string"
            format: "date-time"
          lastSync:
            type: "string"
            format: "date-time"
          extension:
            type: "object"
      name: "MdmProductSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.MdmOperation.shopList"
  summary: "门店列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          startTime:
            type: "string"
            format: "date-time"
          endTime:
            type: "string"
            format: "date-time"
          sortBy:
            type: "string"
          sortType:
            type: "string"
          status:
            type: "string"
      name: "MdmShopListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberType:
                      type: "string"
                    id:
                      type: "string"
                    shopCode:
                      type: "string"
                    shopName:
                      type: "string"
                    shopTypeCode:
                      type: "string"
                    shopType:
                      type: "string"
                    provinceCode:
                      type: "string"
                    provinceName:
                      type: "string"
                    cityCode:
                      type: "string"
                    cityName:
                      type: "string"
                    districtCode:
                      type: "string"
                    districtName:
                      type: "string"
                    address:
                      type: "string"
                    tzCode:
                      type: "string"
                    tzName:
                      type: "string"
                    contactTel:
                      type: "string"
                    status:
                      type: "string"
                    longitude:
                      type: "string"
                    latitude:
                      type: "string"
                    picture:
                      type: "array"
                      items:
                        type: "string"
                    introduction:
                      type: "string"
                    openTime:
                      type: "string"
                      format: "time"
                    closeTime:
                      type: "string"
                      format: "time"
                    bindDepartmentId:
                      type: "string"
                    orgCode:
                      type: "string"
                    orgId:
                      type: "integer"
                      format: "int64"
                    channelType:
                      type: "string"
                    isValid:
                      type: "string"
                    lastSync:
                      type: "string"
                      format: "date-time"
                    extension:
                      type: "object"
      name: "MdmShop>>"
- method: "com.shuyun.apaas.connector.fast.operation.MdmOperation.shopSync"
  summary: "门店同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          memberType:
            type: "string"
          id:
            type: "string"
          shopCode:
            type: "string"
          shopName:
            type: "string"
          shopTypeCode:
            type: "string"
          shopType:
            type: "string"
          provinceCode:
            type: "string"
          provinceName:
            type: "string"
          cityCode:
            type: "string"
          cityName:
            type: "string"
          districtCode:
            type: "string"
          districtName:
            type: "string"
          address:
            type: "string"
          tzCode:
            type: "string"
          tzName:
            type: "string"
          contactTel:
            type: "string"
          status:
            type: "string"
          longitude:
            type: "string"
          latitude:
            type: "string"
          picture:
            type: "array"
            items:
              type: "string"
          introduction:
            type: "string"
          openTime:
            type: "string"
            format: "date-time"
          closeTime:
            type: "string"
            format: "date-time"
          bindDepartmentId:
            type: "string"
          orgCode:
            type: "string"
          orgId:
            type: "integer"
            format: "int64"
          channelType:
            type: "string"
          isValid:
            type: "string"
          lastSync:
            type: "string"
            format: "date-time"
          extension:
            type: "object"
      name: "MdmShopSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.MedalOperation.get"
  summary: "会员勋章查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          medalBizType:
            type: "string"
      name: "MedalGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                memberId:
                  type: "string"
                id:
                  type: "integer"
                  format: "int64"
                name:
                  type: "string"
                effectTime:
                  type: "string"
                  format: "date-time"
                expiredTime:
                  type: "string"
                  format: "date-time"
      name: "Medal>>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.bind"
  summary: "会员绑定"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          scene:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          bindTime:
            type: "string"
            format: "date-time"
          mobile:
            type: "string"
          unionId:
            type: "string"
          appId:
            type: "string"
          openId:
            type: "string"
          registerChannel:
            type: "string"
          registerShopCode:
            type: "string"
          registerShopName:
            type: "string"
          ascriptionChannel:
            type: "string"
          ascriptionChannelName:
            type: "string"
          ascriptionShopCode:
            type: "string"
          ascriptionShopName:
            type: "string"
          fullName:
            type: "string"
          firstName:
            type: "string"
          lastName:
            type: "string"
          headImgUrl:
            type: "string"
          nick:
            type: "string"
          gender:
            type: "string"
          certificateType:
            type: "string"
          certificateNo:
            type: "string"
          email:
            type: "string"
          dateOfBirth:
            type: "string"
          nationality:
            type: "string"
          province:
            type: "string"
          city:
            type: "string"
          district:
            type: "string"
          postalCode:
            type: "string"
          address:
            type: "string"
          job:
            type: "string"
          education:
            type: "string"
          industry:
            type: "string"
          hobby:
            type: "string"
          income:
            type: "string"
          marriage:
            type: "string"
          anniversary:
            type: "string"
          hasChildren:
            type: "string"
          recommender:
            type: "string"
          registerGuide:
            type: "string"
          serviceGuide:
            type: "string"
          appType:
            type: "string"
          extras:
            type: "object"
      name: "MemberBindParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              status:
                type: "string"
      name: "MemberBindResult>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.deregister"
  summary: "会员注销"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          deregisterTime:
            type: "string"
            format: "date-time"
          shopCode:
            type: "string"
          operator:
            type: "string"
          remark:
            type: "string"
      name: "MemberDeregisterParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
      name: "MemberId>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.dynamicCodeGet"
  summary: "会员动态码获取"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          validSeconds:
            type: "integer"
            format: "int32"
      name: "MemberDynamicCodeGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              dynamicCode:
                type: "string"
      name: "DynamicCode>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.dynamicCodeIdentify"
  summary: "会员动态码识别"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          dynamicCode:
            type: "string"
      name: "MemberDynamicCodeIdentifyParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
      name: "MemberId>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.get"
  summary: "会员查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          mobile:
            type: "string"
          unionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          optionalFields:
            type: "array"
            items:
              type: "string"
          channelDataRequired:
            type: "boolean"
      name: "MemberGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              id:
                type: "string"
              brand:
                type: "string"
              registerTime:
                type: "string"
                format: "date-time"
              registerChannel:
                type: "string"
              enrollShopCode:
                type: "string"
              enrollShopName:
                type: "string"
              registerShopCode:
                type: "string"
              registerShopName:
                type: "string"
              ascriptionChannel:
                type: "string"
              ascriptionChannelName:
                type: "string"
              ascriptionShopCode:
                type: "string"
              ascriptionShopName:
                type: "string"
              fullName:
                type: "string"
              gender:
                type: "string"
              mobile:
                type: "string"
              email:
                type: "string"
              dateOfBirth:
                type: "string"
              marriage:
                type: "string"
              status:
                type: "string"
              optionalFieldData:
                type: "object"
              channelData:
                type: "object"
                properties:
                  memberId:
                    type: "string"
                  id:
                    type: "string"
                  brand:
                    type: "string"
                  registerChannel:
                    type: "string"
                  enrollShopCode:
                    type: "string"
                  enrollShopName:
                    type: "string"
                  registerShopCode:
                    type: "string"
                  registerShopName:
                    type: "string"
                  ascriptionChannel:
                    type: "string"
                  ascriptionChannelName:
                    type: "string"
                  ascriptionShopCode:
                    type: "string"
                  ascriptionShopName:
                    type: "string"
                  fullName:
                    type: "string"
                  gender:
                    type: "string"
                  mobile:
                    type: "string"
                  email:
                    type: "string"
                  dateOfBirth:
                    type: "string"
                  marriage:
                    type: "string"
                  status:
                    type: "string"
                  userId:
                    type: "string"
                  unionId:
                    type: "string"
                  appType:
                    type: "string"
                  appId:
                    type: "string"
                  openId:
                    type: "string"
                  mixMobile:
                    type: "string"
                  nick:
                    type: "string"
                  action:
                    type: "string"
                  actionTime:
                    type: "string"
                    format: "date-time"
                  optionalFieldData:
                    type: "object"
      name: "MemberGetResult>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.mobileModify"
  summary: "会员手机号修改"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          mobile:
            type: "string"
      name: "MemberMobileModifyParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
      name: "MemberId>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.modify"
  summary: "会员修改"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          fullName:
            type: "string"
          firstName:
            type: "string"
          lastName:
            type: "string"
          gender:
            type: "string"
          certificateType:
            type: "string"
          certificateNo:
            type: "string"
          dateOfBirth:
            type: "string"
          nationality:
            type: "string"
          province:
            type: "string"
          city:
            type: "string"
          district:
            type: "string"
          postalCode:
            type: "string"
          address:
            type: "string"
          job:
            type: "string"
          education:
            type: "string"
          industry:
            type: "string"
          hobby:
            type: "string"
          income:
            type: "string"
          marriage:
            type: "string"
          anniversary:
            type: "string"
          hasChildren:
            type: "string"
          recommender:
            type: "string"
          email:
            type: "string"
          remark:
            type: "string"
          extras:
            type: "object"
          headImgUrl:
            type: "string"
          nick:
            type: "string"
          modifyTime:
            type: "string"
            format: "date-time"
          mobile:
            type: "string"
          channelType:
            type: "string"
      name: "MemberModifyParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
      name: "MemberId>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.register"
  summary: "会员注册"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          scene:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          registerTime:
            type: "string"
            format: "date-time"
          mobile:
            type: "string"
          unionId:
            type: "string"
          appId:
            type: "string"
          openId:
            type: "string"
          registerChannel:
            type: "string"
          registerShopCode:
            type: "string"
          registerShopName:
            type: "string"
          ascriptionChannel:
            type: "string"
          ascriptionChannelName:
            type: "string"
          ascriptionShopCode:
            type: "string"
          ascriptionShopName:
            type: "string"
          fullName:
            type: "string"
          firstName:
            type: "string"
          lastName:
            type: "string"
          headImgUrl:
            type: "string"
          nick:
            type: "string"
          gender:
            type: "string"
          certificateType:
            type: "string"
          certificateNo:
            type: "string"
          email:
            type: "string"
          dateOfBirth:
            type: "string"
          nationality:
            type: "string"
          province:
            type: "string"
          city:
            type: "string"
          district:
            type: "string"
          postalCode:
            type: "string"
          address:
            type: "string"
          job:
            type: "string"
          education:
            type: "string"
          industry:
            type: "string"
          hobby:
            type: "string"
          income:
            type: "string"
          marriage:
            type: "string"
          anniversary:
            type: "string"
          hasChildren:
            type: "string"
          recommender:
            type: "string"
          registerGuide:
            type: "string"
          serviceGuide:
            type: "string"
          appType:
            type: "string"
          extras:
            type: "object"
          entryType:
            type: "string"
      name: "MemberRegisterParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              status:
                type: "string"
      name: "MemberRegisterResult>"
- method: "com.shuyun.apaas.connector.fast.operation.MemberOperation.unbind"
  summary: "会员解绑"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          registerChannel:
            type: "string"
          registerShopCode:
            type: "string"
          unbindTime:
            type: "string"
            format: "date-time"
      name: "MemberUnbindParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
      name: "MemberId>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.freeze"
  summary: "积分冻结"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          description:
            type: "string"
          shopCode:
            type: "string"
          changeMode:
            type: "string"
          idempotentMode:
            type: "integer"
            format: "int32"
          lockWaitTime:
            type: "integer"
            format: "int32"
          actionName:
            type: "string"
          point:
            type: "integer"
            format: "int32"
          KZZD1:
            type: "string"
          KZZD2:
            type: "string"
          KZZD3:
            type: "string"
      name: "PointFreezeParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.freezeDeduct"
  summary: "消耗已冻结积分"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          freezeTransactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          description:
            type: "string"
          shopCode:
            type: "string"
          changeMode:
            type: "string"
          idempotentMode:
            type: "integer"
            format: "int32"
          lockWaitTime:
            type: "integer"
            format: "int32"
          actionName:
            type: "string"
          KZZD1:
            type: "string"
          KZZD2:
            type: "string"
          KZZD3:
            type: "string"
          reversible:
            type: "boolean"
      name: "PointFreezeDeductParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.get"
  summary: "积分查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
      name: "PointGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              memberId:
                type: "string"
              point:
                type: "integer"
                format: "int32"
      name: "Point>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.modify"
  summary: "积分变更"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          description:
            type: "string"
          shopCode:
            type: "string"
          changeMode:
            type: "string"
          idempotentMode:
            type: "integer"
            format: "int32"
          lockWaitTime:
            type: "integer"
            format: "int32"
          actionName:
            type: "string"
          modifyType:
            type: "string"
          point:
            type: "integer"
            format: "int32"
          effectTime:
            type: "string"
            format: "date-time"
          expiredTime:
            type: "string"
            format: "date-time"
          KZZD1:
            type: "string"
          KZZD2:
            type: "string"
          KZZD3:
            type: "string"
          reversible:
            type: "boolean"
      name: "PointModifyParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.recordsGet"
  summary: "积分明细查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          startTime:
            type: "string"
            format: "date-time"
          endTime:
            type: "string"
            format: "date-time"
          sortBy:
            type: "string"
          sortType:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          recordTypes:
            type: "array"
            items:
              type: "string"
          status:
            type: "array"
            items:
              type: "string"
          shopCode:
            type: "string"
          traceId:
            type: "string"
      name: "PointRecordsGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberId:
                      type: "string"
                    id:
                      type: "string"
                    totalPoint:
                      type: "number"
                      format: "double"
                    recordType:
                      type: "string"
                    description:
                      type: "string"
                    changeMode:
                      type: "string"
                    traceId:
                      type: "string"
                    channelType:
                      type: "string"
                    key:
                      type: "string"
                    shopCode:
                      type: "string"
                    point:
                      type: "number"
                      format: "double"
                    signedPoint:
                      type: "number"
                      format: "double"
                    changeTime:
                      type: "string"
                      format: "date-time"
                    effectTime:
                      type: "string"
                      format: "date-time"
                    expiredTime:
                      type: "string"
                      format: "date-time"
                    recordSourceDetail:
                      type: "string"
                    status:
                      type: "string"
                    extralInfo:
                      type: "string"
                    KZZD1:
                      type: "string"
                    KZZD2:
                      type: "string"
                    KZZD3:
                      type: "string"
      name: "PointRecord>>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.revert"
  summary: "积分交易撤销"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          originalTransactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          description:
            type: "string"
          shopCode:
            type: "string"
          changeMode:
            type: "string"
          actionName:
            type: "string"
          revertType:
            type: "string"
          point:
            type: "integer"
            format: "int32"
          KZZD1:
            type: "string"
          KZZD2:
            type: "string"
          KZZD3:
            type: "string"
      name: "PointRevertParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.PointOperation.unfreeze"
  summary: "积分解冻"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          freezeTransactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          pointBizType:
            type: "string"
          description:
            type: "string"
          shopCode:
            type: "string"
          changeMode:
            type: "string"
          idempotentMode:
            type: "integer"
            format: "int32"
          lockWaitTime:
            type: "integer"
            format: "int32"
          actionName:
            type: "string"
          KZZD1:
            type: "string"
          KZZD2:
            type: "string"
          KZZD3:
            type: "string"
      name: "PointUnfreezeParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.categoryList"
  summary: "标签目录列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          categoryId:
            type: "integer"
            format: "int64"
      name: "TagCategoryListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        $defs:
          com.shuyun.cdp.tags.vo.category.OpenCategoryVo:
            type: "object"
            properties:
              id:
                type: "integer"
                format: "int64"
              name:
                type: "string"
              parentId:
                type: "integer"
                format: "int64"
              rootId:
                type: "integer"
                format: "int64"
              isExternal:
                type: "boolean"
              externalCategoryId:
                type: "string"
              openCategoryVos:
                type: "array"
                items:
                  $ref: "#/$defs/com.shuyun.cdp.tags.vo.category.OpenCategoryVo"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                id:
                  type: "integer"
                  format: "int64"
                name:
                  type: "string"
                parentId:
                  type: "integer"
                  format: "int64"
                rootId:
                  type: "integer"
                  format: "int64"
                isExternal:
                  type: "boolean"
                externalCategoryId:
                  type: "string"
                openCategoryVos:
                  type: "array"
                  items:
                    $ref: "#/$defs/com.shuyun.cdp.tags.vo.category.OpenCategoryVo"
      name: "OpenCategoryVo>>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.list"
  summary: "标签列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          categoryIds:
            type: "array"
            items:
              type: "integer"
              format: "int64"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
      name: "TagListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    id:
                      type: "string"
                    name:
                      type: "string"
                    description:
                      type: "string"
                    origin:
                      type: "string"
                    type:
                      type: "string"
                      enum:
                      - "WITHIN_RULE"
                      - "MANUAL"
                      - "EXTERNAL"
                      - "ENUM_VALUE"
                      - "BEHAVIORAL_COUNT"
                      - "BEHAVIORAL_PREF"
                      - "EARLIEST_LATEST"
                    tagValueType:
                      type: "string"
                      enum:
                      - "HAVE_VALUE"
                      - "HAVE_MULTI_ENUM_VALUE"
                      - "HAVE_MULTI_NUMBER_VALUE"
                      - "HAVE_MULTI_STRING_VALUE"
                      - "HAVE_MULTI_DATE_VALUE"
                      - "NO_VALUE"
                    customerCount:
                      type: "integer"
                      format: "int64"
                    updatePolicy:
                      type: "string"
                      enum:
                      - "ONLY_ONCE"
                      - "MANUAL"
                      - "AUTO"
                      - "MANUAL_TAG"
                    status:
                      type: "string"
                      enum:
                      - "DELETED"
                      - "OFFLINE"
                      - "SUBMITTING"
                      - "WAIT_RETRY"
                      - "SUBMIT_SUCCESSFUL"
                      - "SUBMIT_FAILED"
                      - "NOT_CALCULATE"
                      - "WAIT_CALCULATE"
                      - "CALCULATING"
                      - "CALCULATE_SUCCESSFUL"
                      - "CALCULATE_FAILED"
                    categoryId:
                      type: "integer"
                      format: "int64"
                    categoryPath:
                      type: "string"
                    externalTagId:
                      type: "string"
                    manualTagFqn:
                      type: "string"
                    charValues:
                      type: "array"
                      items:
                        type: "string"
      name: "OpenTagVo>>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.tagging"
  summary: "标签打标"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          operator:
            type: "string"
          tagId:
            type: "string"
          tagValues:
            type: "array"
            items:
              type: "string"
          batchTagging:
            type: "boolean"
          memberIds:
            type: "array"
            items:
              type: "string"
          tags:
            type: "array"
            items:
              type: "object"
              properties:
                tagId:
                  type: "string"
                tagValues:
                  type: "array"
                  items:
                    type: "object"
                    required: []
                    properties: {}
                mark:
                  type: "boolean"
                operateTagType:
                  type: "string"
                  enum:
                  - "APPEND"
                  - "REPLACE"
                  - "REMOVE"
                  - "CLEAR"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
      name: "TagTaggingParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.untagging"
  summary: "标签去标"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          operator:
            type: "string"
          tagId:
            type: "string"
          tagValues:
            type: "array"
            items:
              type: "string"
          batchUntagging:
            type: "boolean"
          memberIds:
            type: "array"
            items:
              type: "string"
          tags:
            type: "array"
            items:
              type: "object"
              properties:
                tagId:
                  type: "string"
                tagValues:
                  type: "array"
                  items:
                    type: "object"
                    required: []
                    properties: {}
                mark:
                  type: "boolean"
                operateTagType:
                  type: "string"
                  enum:
                  - "APPEND"
                  - "REPLACE"
                  - "REMOVE"
                  - "CLEAR"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
      name: "TagUntaggingParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.userList"
  summary: "标签下用户列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
      name: "TagUserListParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "string"
      name: "String>>"
- method: "com.shuyun.apaas.connector.fast.operation.TagOperation.userTags"
  summary: "用户标签列表"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          tagIds:
            type: "array"
            items:
              type: "string"
      name: "TagUserTagsParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "array"
            items:
              type: "object"
              properties:
                tagId:
                  type: "string"
                name:
                  type: "string"
                tagValues:
                  type: "array"
                  items:
                    type: "string"
      name: "OpenTagContent>>"
- method: "com.shuyun.apaas.connector.fast.operation.TradeOperation.get"
  summary: "订单查询"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          page:
            type: "integer"
            format: "int32"
          pageSize:
            type: "integer"
            format: "int32"
          startTime:
            type: "string"
            format: "date-time"
          endTime:
            type: "string"
            format: "date-time"
          sortBy:
            type: "string"
          sortType:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          orderType:
            type: "string"
          status:
            type: "string"
          channels:
            type: "array"
            items:
              type: "string"
          orderId:
            type: "string"
      name: "TradeGetParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
            properties:
              page:
                type: "integer"
                format: "int32"
              pageSize:
                type: "integer"
                format: "int32"
              totalCount:
                type: "integer"
                format: "int64"
              totalPage:
                type: "integer"
                format: "int32"
              items:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberId:
                      type: "string"
                    shopTypeCode:
                      type: "string"
                    channelType:
                      type: "string"
                    totalFee:
                      type: "number"
                      format: "double"
                    payment:
                      type: "number"
                      format: "double"
                    discountRate:
                      type: "number"
                      format: "double"
                    discountFee:
                      type: "number"
                      format: "double"
                    extension:
                      type: "object"
                    customerNo:
                      type: "string"
                    orderId:
                      type: "string"
                    originOrderId:
                      type: "string"
                    shopCode:
                      type: "string"
                    shopName:
                      type: "string"
                    totalQuantity:
                      type: "integer"
                      format: "int32"
                    orderType:
                      type: "string"
                    orderStatus:
                      type: "string"
                    freight:
                      type: "number"
                      format: "double"
                    receiverName:
                      type: "string"
                    receiverMobile:
                      type: "string"
                    receiverTelephone:
                      type: "string"
                    receiverProvince:
                      type: "string"
                    receiverCity:
                      type: "string"
                    receiverDistrict:
                      type: "string"
                    receiverAddress:
                      type: "string"
                    receiverZipCode:
                      type: "string"
                    couponFee:
                      type: "number"
                      format: "double"
                    guideCode:
                      type: "string"
                    description:
                      type: "string"
                    pointFlag:
                      type: "integer"
                      format: "int32"
                    isInternal:
                      type: "string"
                    isSend:
                      type: "string"
                    id:
                      type: "string"
                    payTime:
                      type: "string"
                      format: "date-time"
                    shippingTime:
                      type: "string"
                      format: "date-time"
                    receiveTime:
                      type: "string"
                      format: "date-time"
                    exchangeTime:
                      type: "string"
                      format: "date-time"
                    refundTime:
                      type: "string"
                      format: "date-time"
                    finishTime:
                      type: "string"
                      format: "date-time"
                    orderTime:
                      type: "string"
                      format: "date-time"
                    updateTime:
                      type: "string"
                      format: "date-time"
                    lastSync:
                      type: "string"
                      format: "date-time"
                    orderItems:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          memberId:
                            type: "string"
                          id:
                            type: "string"
                          shopTypeCode:
                            type: "string"
                          channelType:
                            type: "string"
                          totalFee:
                            type: "number"
                            format: "double"
                          payment:
                            type: "number"
                            format: "double"
                          discountRate:
                            type: "number"
                            format: "double"
                          discountFee:
                            type: "number"
                            format: "double"
                          orderItemId:
                            type: "string"
                          originOrderItemId:
                            type: "string"
                          productCode:
                            type: "string"
                          productName:
                            type: "string"
                          quantity:
                            type: "integer"
                            format: "int32"
                          status:
                            type: "string"
                          skuId:
                            type: "string"
                          tagPrice:
                            type: "number"
                            format: "double"
                          retailPrice:
                            type: "number"
                            format: "double"
                          orderType:
                            type: "string"
                          picture:
                            type: "array"
                            items:
                              type: "string"
                          orderTime:
                            type: "string"
                            format: "date-time"
                          finishTime:
                            type: "string"
                            format: "date-time"
                          updateTime:
                            type: "string"
                            format: "date-time"
                          lastSync:
                            type: "string"
                            format: "date-time"
                          orderId:
                            type: "string"
                          shopName:
                            type: "string"
                          shopCode:
                            type: "string"
                          extension:
                            type: "object"
      name: "TradeMainOrder>>"
- method: "com.shuyun.apaas.connector.fast.operation.TradeOperation.orderSync"
  summary: "订单同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          order:
            type: "object"
            properties:
              memberId:
                type: "string"
              memberType:
                type: "string"
              orderOwnerType:
                type: "string"
              member:
                type: "object"
                additionalProperties:
                  type: "string"
              id:
                type: "string"
              shopTypeCode:
                type: "string"
              channelType:
                type: "string"
              orderType:
                type: "string"
              totalFee:
                type: "number"
                format: "double"
              payment:
                type: "number"
                format: "double"
              discountRate:
                type: "number"
                format: "double"
              discountFee:
                type: "number"
                format: "double"
              finishTime:
                type: "string"
                format: "date-time"
              orderTime:
                type: "string"
                format: "date-time"
              payTime:
                type: "string"
                format: "date-time"
              customerNo:
                type: "string"
              orderId:
                type: "string"
              shopCode:
                type: "string"
              shopName:
                type: "string"
              shippingTime:
                type: "string"
                format: "date-time"
              receiveTime:
                type: "string"
                format: "date-time"
              updateTime:
                type: "string"
                format: "date-time"
              couponNo:
                type: "string"
              couponFee:
                type: "number"
                format: "double"
              guideCode:
                type: "string"
              orderStatus:
                type: "string"
              orderCoupons:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberType:
                      type: "string"
                    id:
                      type: "string"
                    channelType:
                      type: "string"
                    orderItemId:
                      type: "string"
                    projectId:
                      type: "string"
                    projectName:
                      type: "string"
                    instanceId:
                      type: "string"
                    couponNo:
                      type: "string"
                    couponFee:
                      type: "number"
                      format: "double"
                    orderId:
                      type: "string"
                    lastSync:
                      type: "string"
                      format: "date-time"
              orderPays:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberType:
                      type: "string"
                    id:
                      type: "string"
                    channelType:
                      type: "string"
                    payWayCode:
                      type: "string"
                    payWayName:
                      type: "string"
                    payment:
                      type: "number"
                      format: "double"
                    currency:
                      type: "string"
                    orderId:
                      type: "string"
                    lastSync:
                      type: "string"
                      format: "date-time"
              orderItems:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberType:
                      type: "string"
                    orderOwnerType:
                      type: "string"
                    id:
                      type: "string"
                    shopTypeCode:
                      type: "string"
                    shopCode:
                      type: "string"
                    shopName:
                      type: "string"
                    channelType:
                      type: "string"
                    orderType:
                      type: "string"
                    totalFee:
                      type: "number"
                      format: "double"
                    payment:
                      type: "number"
                      format: "double"
                    discountRate:
                      type: "number"
                      format: "double"
                    discountFee:
                      type: "number"
                      format: "double"
                    finishTime:
                      type: "string"
                      format: "date-time"
                    orderTime:
                      type: "string"
                      format: "date-time"
                    payTime:
                      type: "string"
                      format: "date-time"
                    extension:
                      type: "object"
                    status:
                      type: "string"
                    orderId:
                      type: "string"
                    orderItemId:
                      type: "string"
                    productCode:
                      type: "string"
                    productName:
                      type: "string"
                    quantity:
                      type: "integer"
                      format: "int32"
                    tagPrice:
                      type: "number"
                      format: "double"
                    retailPrice:
                      type: "number"
                      format: "double"
                    skuId:
                      type: "string"
                    picture:
                      type: "array"
                      items:
                        type: "string"
                    lastSync:
                      type: "string"
                      format: "date-time"
              totalQuantity:
                type: "integer"
                format: "int32"
              freight:
                type: "number"
                format: "double"
              receiverName:
                type: "string"
              receiverMobile:
                type: "string"
              receiverTelephone:
                type: "string"
              receiverProvince:
                type: "string"
              receiverCity:
                type: "string"
              receiverDistrict:
                type: "string"
              receiverAddress:
                type: "string"
              receiverZipCode:
                type: "string"
              description:
                type: "string"
              isInternal:
                type: "string"
              pointFlag:
                type: "integer"
                format: "int32"
              lastSync:
                type: "string"
                format: "date-time"
              extension:
                type: "object"
              isSend:
                type: "string"
      name: "TradeOrderSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
- method: "com.shuyun.apaas.connector.fast.operation.TradeOperation.refundSync"
  summary: "退单同步"
  content:
    request:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          tenantId:
            type: "string"
          bizCode:
            type: "string"
          requestChannel:
            type: "string"
          requestSystem:
            type: "string"
          transactionId:
            type: "string"
          identify:
            type: "object"
            properties:
              memberId:
                type: "string"
              channel:
                type: "string"
              userId:
                type: "string"
          refund:
            type: "object"
            properties:
              memberId:
                type: "string"
              memberType:
                type: "string"
              orderOwnerType:
                type: "string"
              member:
                type: "object"
                additionalProperties:
                  type: "string"
              id:
                type: "string"
              shopTypeCode:
                type: "string"
              channelType:
                type: "string"
              refundTime:
                type: "string"
                format: "date-time"
              orderTime:
                type: "string"
                format: "date-time"
              finishTime:
                type: "string"
                format: "date-time"
              payment:
                type: "number"
                format: "double"
              totalFee:
                type: "number"
                format: "double"
              originOrderId:
                type: "string"
              orderStatus:
                type: "string"
              refundOrderItems:
                type: "array"
                items:
                  type: "object"
                  properties:
                    memberType:
                      type: "string"
                    orderOwnerType:
                      type: "string"
                    id:
                      type: "string"
                    shopTypeCode:
                      type: "string"
                    channelType:
                      type: "string"
                    extension:
                      type: "object"
                    updateTime:
                      type: "string"
                      format: "date-time"
                    orderTime:
                      type: "string"
                      format: "date-time"
                    finishTime:
                      type: "string"
                      format: "date-time"
                    payment:
                      type: "number"
                      format: "double"
                    totalFee:
                      type: "number"
                      format: "double"
                    originOrderId:
                      type: "string"
                    originOrderItemId:
                      type: "string"
                    status:
                      type: "string"
                    orderId:
                      type: "string"
                    orderItemId:
                      type: "string"
                    productCode:
                      type: "string"
                    productName:
                      type: "string"
                    quantity:
                      type: "integer"
                      format: "int32"
                    skuId:
                      type: "string"
                    picture:
                      type: "array"
                      items:
                        type: "string"
                    lastSync:
                      type: "string"
                      format: "date-time"
                    shopCode:
                      type: "string"
                    shopName:
                      type: "string"
                    orderType:
                      type: "string"
              customerNo:
                type: "string"
              orderId:
                type: "string"
              shopCode:
                type: "string"
              shopName:
                type: "string"
              totalQuantity:
                type: "integer"
                format: "int32"
              freight:
                type: "number"
                format: "double"
              receiverName:
                type: "string"
              receiverMobile:
                type: "string"
              receiverTelephone:
                type: "string"
              receiverProvince:
                type: "string"
              receiverCity:
                type: "string"
              receiverDistrict:
                type: "string"
              receiverAddress:
                type: "string"
              receiverZipCode:
                type: "string"
              description:
                type: "string"
              pointFlag:
                type: "integer"
                format: "int32"
              lastSync:
                type: "string"
                format: "date-time"
              extension:
                type: "object"
              isSend:
                type: "string"
              isInternal:
                type: "string"
              orderType:
                type: "string"
      name: "TradeRefundSyncParam"
    response:
      schema:
        $schema: "https://json-schema.org/draft/2020-12/schema"
        type: "object"
        properties:
          success:
            type: "boolean"
          code:
            type: "string"
          message:
            type: "string"
          data:
            type: "object"
      name: "Void>"
