000000=success
100000=api path error
100001=required param error[%s]
100002=member identify param error[%s and %s can not be all empty.]
100003=member identify param error[%s or %s must be empty.]
100004=member identify param error[%s and %s both required.]
100005=member identify param error[%s must be empty.]
100006=member identify param error[%s must not be empty.]
100007=time pair param error[the %s must be less than %s.]
100008=time pair param error[the time interval between %s and %s must be less than %s %s.]
100009=member identify param error[{0} and {1} and {2} can not be all empty.]
100010=member identify param error[the three param {0},{1} and {2} only one required.]

500000=biz error[found no member by userId:%s]
500001=biz error[found no %s biz cache config]


500100=server error[%s]
500200=member bind scene error[%s]
500201=member register scene error[%s]
500202=member unbind scene error[%s]
500203=member query scene error[%s]
500204=member deregister scene error[%s]
500205=member modify scene error[%s]
500206=member mobile modify scene error[%s]
500207=member dynamic code get scene error[%s]
500208=member dynamic code identify scene error[%s]


500230=coupon consume scene error[%s]
500231=coupon consume repeal scene error[%s]
500232=coupon discount calc scene error[%s]
500233=coupon get scene error[found no coupon by code:%s]
500234=coupon grant scene error[%s]
500235=coupon grant repeal scene error[%s]
500236=coupon lock scene error[%s]
500237=coupon unlock scene error[%s]
500238=coupon project get scene error[found no project by id:%s]
500239=coupon transfer scene error[%s]
500240=coupon receive scene error[%s]
500241=coupon return scene error[%s]
500242=coupon project list scene error

500300=trade order sync scene error[%s]
500301=trade refund sync scene error[%s]
500302=trade order get scene error[%s]


500400=tag category list scene error[%s]
500401=tag list scene error[%s]
500402=tag tagging scene error[%s]
500403=tag untagging scene error[%s]
500404=tag user tags scene error[%s]