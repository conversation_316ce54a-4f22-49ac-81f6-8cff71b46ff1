{"fqn": "data.prctvmkt.fast.BizCache", "title": "fast-service服务业务缓存配置", "owner": "prctvmkt", "description": "", "tag": null, "type": "Object", "trait": {"compositeIndexes": null, "partitionInfo": null, "distributeInfo": null, "snMapping": null, "bindSnMapping": null, "occIdMapping": null, "bindOccIdMapping": null, "elasticPhysicalConfig": null, "enabledTags": false}, "elementType": null, "originalType": null, "mainModelFqn": null, "rawFqn": null, "enums": null, "custom": true, "embedded": false, "enableElastic": false, "visible": true, "isPrivate": false, "isTemporary": false, "logicalDelete": false, "extensible": false, "insertable": true, "updatable": false, "autoMapping": null, "useMode": "OLTP", "hints": null, "fields": [{"sensitiveType": null, "visible": true, "indexed": false, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": "id", "title": "id", "tags": null, "isExtension": false, "ignoreCase": false, "name": "id", "sensitiveGrade": null, "constraint": {"nullable": false, "unique": true, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": true, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": 8, "fractionalLength": null, "stringLength": 32, "pattern": null}, "tag": "id", "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.Id", "mainModelFqn": null, "destSubscribedFqn": null, "type": "Id", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": true, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "租户id", "tags": null, "isExtension": false, "ignoreCase": false, "name": "tenantId", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": 64, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.String", "mainModelFqn": null, "destSubscribedFqn": null, "type": "String", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": true, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "业务编码", "tags": null, "isExtension": false, "ignoreCase": false, "name": "bizCode", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": 64, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.String", "mainModelFqn": null, "destSubscribedFqn": null, "type": "String", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": true, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "缓存类型", "tags": null, "isExtension": false, "ignoreCase": false, "name": "cacheType", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": 64, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.String", "mainModelFqn": null, "destSubscribedFqn": null, "type": "String", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": false, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "缓存值", "tags": null, "isExtension": false, "ignoreCase": false, "name": "value", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": 1024, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.String", "mainModelFqn": null, "destSubscribedFqn": null, "type": "String", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": false, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "创建时间", "tags": null, "isExtension": false, "ignoreCase": false, "name": "createTime", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": null, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.DateTime", "mainModelFqn": null, "destSubscribedFqn": null, "type": "DateTime", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}, {"sensitiveType": null, "visible": true, "indexed": false, "custom": true, "_i18nPayload": null, "refType": "ScalarNested", "description": null, "title": "更新时间", "tags": null, "isExtension": false, "ignoreCase": false, "name": "updateTime", "sensitiveGrade": null, "constraint": {"nullable": true, "unique": false, "autoIncrement": null, "stringId": true, "encrypted": false, "cryptoKeyCode": null, "generatedValue": false, "enableAllModify": false, "defaultValue": null, "minExclusive": null, "minValue": null, "maxExclusive": null, "maxValue": null, "integerLength": null, "fractionalLength": null, "stringLength": null, "pattern": null}, "tag": null, "enableSensitive": false, "fieldType": {"owner": "System", "subscribed": false, "fqn": "system.lang.DateTime", "mainModelFqn": null, "destSubscribedFqn": null, "type": "DateTime", "embedded": false}, "i18nEnabled": false, "mappedBy": null, "status": "COMMITTED"}], "metricsFields": null, "derivedFields": null, "extensionMetas": null, "tagMetas": null, "isDraft": true, "status": "COMMITTED", "lastCommitId": null, "subscribed": false, "destSubscribedFqn": null, "occIdEnabled": false, "createTime": "2025-02-20T01:42:31.000Z", "createBy": "cxl", "updateTime": "2025-02-20T01:45:57.000Z", "updateBy": "cxl", "tenantId": "a0srcee93y", "_i18nPayload": null, "dataModelId": "data.prctvmkt.fast.BizCache_1"}