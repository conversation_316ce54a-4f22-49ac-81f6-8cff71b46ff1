# fast-connector

    fast integration kylin crm product modules and provide standard tools

### 注意事项
#### 1、resources/metadata目录下文件为apaas-connector-maven-plugin自动生成

#### 2、拉取代码后将pom文件中artifactId值从fast-connector-project修改为fast-connector-项目编码,如fast-connector-hnxh

#### 3、拉取代码后将FastConnector类的Title注解的值"项目名称"替换为具体项目名称,如河南新华修改后值为"fast-connctor-河南新华"

#### 4、拉取代码后将FastConnector类的Configuration注解的tenant值修改为为具体项目租户

#### 5、api接口响应码响应信息需在messages_en_US.properties统一管理,不要在代码中随意log输出

#### 6、对外接口时间字段入参出参统一使用北京时区LocalDateTime,数据模型时间字段请统一使用DateTime

#### 7、运行配置添加以下环境变量
    art.logging.level=root:WARN,com.shuyun.apaas:INFO,com.shuyun:INFO
    art.vertx.validator.format=false
    system.discovery.enable=true
    system.api.address=数据服务api域名
    system.eventService.userName=fastconnector
    system.eventService.secret=事件服务secret
    passport.client.http.token.appKey=metadata
    passport.client.http.token.appSecret=api秘钥
    passport.client.http.token.tenantId=环境编码
    vertx.workerPoolSize=128
    API_VERSION=v1
    spectrum.disableAutoUploadProperties=true
    SANDBOX=base
    ENVIRONMENT=具体环境编码
    system.config.address=配置中心地址
    spectrum.key=配置中心key
    spectrum.secret=配置中心secret

#### 8、卡券项目缓存刷新策略:启动全量加载+增量监听事件服务更新缓存,为避免因为卡券项目配置的选择器数据量过大出现服务oom,对单个选择器缓存数据限制1w条.有特殊场景需要超过此限制,随时联系沟通解决方案

#### 9、卡券项目自定义属性默认动态扩展到CouponProject对象,如果想统一设置到extData集合,请设置getExtData方法@JsonAnyGetter(enabled = false)

#### 10、低版本的dataapi级联保存会删除退单子订单表orderitem关联字段为空的记录,使用订单接口请保证dataapi在1.42.1版本之上

#### 11、连接器代码deploy之前必须先pull远程代码并将本地代码push到远程后才可以deploy,不允许deploy未提交的代码,也不允许不拉取远程代码直接deploy

#### 12、服务第一次启动后需要配置data.prctvmkt.fast.BizCache模型(服务自动创建)各模块缓存数据(从其他项目复制后修改)

#### 13、序列化反序列化统一采用jackson,禁止使用阿里的fastjson

#### 14、FastConfig常见配置说明: 
        请求的kylin客户端id 值应配置为pom文件中12行的artifactId值,如(fast-connector-hnxh)
        请求的kylin客户端密钥 值应配置为配置中心system.api.secret属性值

#### 15、框架底层调用麒麟产品api默认走客户端服务发现,注意spectrum.key对应的配置中心账号需要有服务发现权限(找运维申请)