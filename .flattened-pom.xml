<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.shuyun.apaas</groupId>
    <artifactId>apaas-devkit-parent</artifactId>
    <version>1.7.1</version>
  </parent>
  <groupId>com.shuyun.fast</groupId>
  <artifactId>fast-connector-jdb</artifactId>
  <version>1.0.1-SNAPSHOT</version>
  <name>${project.artifactId}</name>
  <licenses>
    <license>
      <name><PERSON>yun Lcap</name>
      <url>https://lcap.shuyun.com</url>
    </license>
  </licenses>
  <distributionManagement>
    <repository>
      <id>releases</id>
      <name>apaas</name>
      <url>https://nexus.shuyun.com/repository/releases</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>apaas</name>
      <url>https://nexus.shuyun.com/repository/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <es.eventservice.version>1.9.6.RC3</es.eventservice.version>
    <uuid.version>4.3.0</uuid.version>
    <dataapi.version>1.40.0.RELEASE</dataapi.version>
    <kafka.client.version>3.6.2</kafka.client.version>
    <redisson.version>3.23.3</redisson.version>
    <fast.sdk.version>0.1.20</fast.sdk.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.shuyun.apaas</groupId>
      <artifactId>apaas-connector-devkit-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.shuyun.apaas</groupId>
      <artifactId>apaas-connector-devkit-kylin</artifactId>
    </dependency>
    <dependency>
      <groupId>com.lcap.qing</groupId>
      <artifactId>qing-retrofit</artifactId>
      <version>1.0.10</version>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.shuyun</groupId>
      <artifactId>fast-open-sdk</artifactId>
      <version>${fast.sdk.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.shuyun.dm</groupId>
          <artifactId>dm-dataapi-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.dm</groupId>
          <artifactId>dm-sdk-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.shuyun.ticket</groupId>
      <artifactId>ticket-support</artifactId>
      <version>1.12.0</version>
      <exclusions>
        <exclusion>
          <groupId>com.shuyun.kylin.crm</groupId>
          <artifactId>data-selector-sdk</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.shuyun.dm</groupId>
      <artifactId>dm-dataapi-sdk</artifactId>
      <version>${dataapi.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.shuyun.lite.module</groupId>
          <artifactId>lite-passport-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>discovery-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>discovery-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>client-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>client-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>configuration-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>configuration-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>joda-time</groupId>
          <artifactId>joda-time</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.squareup.okhttp3</groupId>
          <artifactId>okhttp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.ant</groupId>
          <artifactId>ant</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>3.1.8</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
      <version>${redisson.version}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.kafka</groupId>
      <artifactId>kafka-clients</artifactId>
      <version>${kafka.client.version}</version>
    </dependency>
    <dependency>
      <groupId>com.shuyun.es</groupId>
      <artifactId>es-eventservice-sdk</artifactId>
      <version>${es.eventservice.version}</version>
      <exclusions>
        <exclusion>
          <groupId>com.shuyun.lite.module</groupId>
          <artifactId>lite-sdk-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.lite.module</groupId>
          <artifactId>lite-passport-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.spectrum</groupId>
          <artifactId>discovery-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.shuyun.lite.module</groupId>
          <artifactId>lite-base</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.bval</groupId>
      <artifactId>bval-jsr</artifactId>
      <version>2.0.6</version>
    </dependency>
    <dependency>
      <groupId>com.shuyun.air.kylin</groupId>
      <artifactId>micronaut-support</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.uuid</groupId>
      <artifactId>java-uuid-generator</artifactId>
      <version>${uuid.version}</version>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>apaas</id>
      <url>https://nexus.shuyun.com/repository/public</url>
    </repository>
  </repositories>
  <build>
    <plugins>
      <plugin>
        <groupId>com.shuyun.apaas</groupId>
        <artifactId>apaas-connector-maven-plugin</artifactId>
        <configuration>
          <skip>false</skip>
          <username>mengjiaxing2141</username>
          <password>CGskAfhW</password>
          <url>https://graph.apaas.shuyun.com</url>
          <mainClass>com.shuyun.apaas.connector.fast.FastConnector</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
